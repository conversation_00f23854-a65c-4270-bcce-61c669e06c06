{"ConfigVersion": 3, "Entries": [{"MinAvailableMemoryMB": 50, "JobNotificationEmail": "<EMAIL>", "Skip": false, "Fuzzer": {"$type": "libfuzzer", "FuzzingHarnessExecutableName": "fuzzloaders.exe", "FuzzingTargetBinaries": ["fuzzloaders.exe"]}, "RebootAfterSetup": false, "OneFuzzJobs": [{"ProjectName": "Direct3D", "TargetName": "DirectXTex", "TargetOptions": [" -rss_limit_mb=4096"], "TargetEnv": {"ASAN_OPTIONS": "allocator_may_return_null=1"}}], "JobDependencies": ["fuzzloaders.exe", "fuzzloaders.pdb", "clang_rt.asan_dynamic-x86_64.dll", "msdia140.dll", "setup.ps1"], "AdoTemplate": {"Org": "microsoft", "Project": "OS", "AssignedTo": "<EMAIL>", "AreaPath": "OS\\Core\\SiGMa\\GRFX-Graphics", "IterationPath": "OS\\Future"}, "CodeCoverage": {"Org": "mscodehub", "Project": "DirectXTex", "PipelineId": "3048"}}]}