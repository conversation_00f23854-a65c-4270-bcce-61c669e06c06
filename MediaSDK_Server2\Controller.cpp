﻿#include "Controller.h"
#include "ModeSceneMgr.h"
#include "SourceMgr.h"
#include "AudioMgr.h"
#include "FilterMgr.h"
#include "MonitorMgr.h"
#include "LogDumpMgr.h"
#include "WatchdogClient.h"
#include "icief.h"
#include "ciefhelper.h"
#include "SystemHook.h"
#include "AiSdkIPCMgr.h"
#include "MediaSDKControllerV2Impl.h"
#include <unordered_set>
#include <dxgi1_6.h>
#include <DirectXTex.h>
#include <filesystem>

extern CIEF::ICIEF* g_cief;
extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;
media_mgr::MediaMgr* g_mediaMgr = nullptr;
v3::AudioFrameProcessor* g_audioFrameProcessor = nullptr;
v3::VideoFrameProcessor* g_videoFrameProcessor = nullptr;

Controller::Controller()
{
	g_mediaMgr = new media_mgr::MediaMgr;
}

Controller::~Controller() 
{}

void Controller::OnParallelInitialize(void* param){}

void Controller::Close()
{
	IWorkflowMonitor* quitFlow = (IWorkflowMonitor*)g_cief->QueryInterface(LSINAME_SHUTDOWNFLOW);
	CloseTimer();
	quitFlow->Record("Controller::Close 1");
	g_mediaMgr->Uninitialize();
	quitFlow->Record("Controller::Close 2");
}

bool Controller::Initialize(INITIALIZE_INFO* info)
{
	if (!info)
	{
		LOG(ERROR) << "[Controller::Initialize] initialize param is null";
		return false;
	}

	// ::MessageBoxA(0, "WinMain", 0, 0);
	m_bottomWnd = info->bottomWnd;
	if (!g_mediaMgr->Initialize(*info))
	{
		LOG(ERROR) << "[Controller::Initialize] MediaMgr Initialize failed!";
		return false;
	}

	g_audioFrameProcessor = g_sdkController->GetAudioFrameProcessor();
    g_videoFrameProcessor = g_sdkController->GetVideoFrameProcessor();
	g_cief->GetThreadMgr()->AddTaskToThreadPool(new MemberTask<Controller>(this, &Controller::SetupTimer, 0));
	LS::LOCALKEYBORAD::GetInstance().KEYBOARD_HOOK += Delegate<void(WPARAM, LPARAM)>(this, &Controller::OnKey);
	return true;
}

void Controller::Quit()
{
    g_cief->GetEventMgr()->GetEvent(&LSEVENT_QUIT)->CallBack(0, 0);
}

void Controller::EnumWindows(BOOL icon, std::vector<WINDOW_DESC>* oWindowDescs)
{
	g_sdkController->EnumWindows(icon, oWindowDescs);
}

void Controller::EnumMonitors(std::vector<MONITOR_DESC>* oMonitorDescs)
{
	g_sdkController->EnumMonitors(oMonitorDescs);
}

void Controller::EnumVideoDevices(std::vector<DSHOW>* oDevices)
{
	g_sdkController->EnumVideoInputDevices(oDevices);
}

void Controller::EnumAudioDevices(std::vector<DSHOW>* oDevices)
{
	g_sdkController->EnumAudioInputDevices(oDevices);
}

void Controller::EnumCaptureFormats(const DSHOW& videoDevice, std::vector<VIDEO_CAPTURE_FORMAT>* videoCapFormats, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats)
{
    if (videoCapFormats)
    {
        if (audioCapFormats)
        {
			g_sdkController->EnumDeviceSupportFormats(videoDevice, videoCapFormats, audioCapFormats);
        }
        else
        {
			g_sdkController->EnumDeviceSupportFormats(videoDevice, videoCapFormats, nullptr); // Camera does not need to enumerate audio formats
        }
    }
}

void Controller::EnumAudioCapFormatWithDShowName(const DSHOW& device, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats)
{
    g_sdkController->EnumDeviceSupportFormatsByDeviceName(device, audioCapFormats);
}

void Controller::IsForegroundFullScreen(bool* full)
{
    HWND hWND = NULL;
    hWND = GetForegroundWindow();
    if ((hWND != GetDesktopWindow()) && (hWND != GetShellWindow()))
    {
        RECT windowRect = {0};
        GetWindowRect(hWND, &windowRect);
        RECT deskRect = {0};
        GetWindowRect(GetDesktopWindow(), &deskRect);
        if (windowRect.left <= deskRect.left &&
            windowRect.top <= deskRect.top &&
            windowRect.right >= deskRect.right &&
            windowRect.bottom >= deskRect.bottom)
        {
            WCHAR szTemp[100];
            if (GetClassNameW(hWND, szTemp, _countof(szTemp)) > 0)
            {
				if (wcscmp(szTemp, L"WorkerW") != 0)
					*full = true;
            }
        }
    }
}

void Controller::WindowOverlapped(HWND hwnd, bool* overlapped)
{
	if (hwnd == NULL)
	{
		*overlapped = false;
		return;
	}

	if (!IsWindowVisible(hwnd) || IsIconic(hwnd) || hwnd == GetTopWindow(GetDesktopWindow()))
	{
		*overlapped = true;
		return;
	}

	RECT rect;
	GetWindowRect(hwnd, &rect);
	HWND hCur = GetWindow(hwnd, GW_HWNDPREV);
	while (hCur != NULL)
	{
		if (IsWindowVisible(hCur) && !IsIconic(hCur))
		{
			RECT curRect;
			GetWindowRect(hCur, &curRect);
			*overlapped = !(curRect.left > rect.right ||
				curRect.right < rect.left ||
				curRect.top > rect.bottom ||
				curRect.bottom < rect.top);
			if (*overlapped)
				break;
		}
		hCur = GetNextWindow(hCur, GW_HWNDPREV);
	}
}

void Controller::GetWindowOverlappedMonitors(HWND hwnd, std::vector<std::string>* monitorDids, std::string* mainDid, std::vector<std::string>* allDidList)
{
    USER_MONITOR_INFO info{};

    if (!::GetWindowRect(hwnd, &info.windowRect))
    {
        if (monitorDids)
        {
            for (const auto& monitor : info.monitors)
            {
                monitorDids->push_back(monitor);
            }
            return;
        }
    }

    static auto MonitorEnumCallback = [](HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) -> BOOL {
        USER_MONITOR_INFO* info = reinterpret_cast<USER_MONITOR_INFO*>(dwData);
        MONITORINFOEXA     monitorInfo;
		monitorInfo.cbSize = sizeof(MONITORINFOEXA);

        if (::GetMonitorInfoA(hMonitor, &monitorInfo))
        {
            DISPLAY_DEVICEA displayDevice{};
			displayDevice.cb = sizeof(displayDevice);
            if (::EnumDisplayDevicesA(monitorInfo.szDevice, 0, &displayDevice,
				EDD_GET_DEVICE_INTERFACE_NAME))
            {
                info->allMonitorDidList.push_back(displayDevice.DeviceID);

                RECT intersection;
                if (::IntersectRect(&intersection, &info->windowRect, &monitorInfo.rcMonitor))
                {
                    info->monitors.push_back(displayDevice.DeviceID);
                }
            }
        }
        return TRUE;
    };

    MONITORENUMPROC enumProc = MonitorEnumCallback;
    LPARAM          lParam = reinterpret_cast<LPARAM>(&info);

    ::EnumDisplayMonitors(NULL, NULL, enumProc, lParam);
    if (monitorDids)
    {
        for (const auto& monitor : info.monitors)
        {
            monitorDids->push_back(monitor);
        }
    }
    if (mainDid)
    {
        auto h = MonitorFromWindow(NULL, MONITOR_DEFAULTTOPRIMARY);
        if (h)
        {
            MONITORINFOEXA mi;
            mi.cbSize = sizeof(MONITORINFOEXA);

            if (::GetMonitorInfoA(h, &mi))
            {
                DISPLAY_DEVICEA device{};
                device.cb = sizeof(device);
                if (::EnumDisplayDevicesA(mi.szDevice, 0, &device,
                                          EDD_GET_DEVICE_INTERFACE_NAME))
                {
                    *mainDid = device.DeviceID;
                }
            }
        }
    }
    if (allDidList)
    {
        for (const auto& did : info.allMonitorDidList)
        {
            allDidList->push_back(did);
        }
    }
}

void Controller::BonjourCheck(bool* oChecked)
{
	g_sdkController->MobileProjectorSourceCheckBonjour(oChecked);
}

void Controller::OpenMobilePreview(UINT32 videoModel, UINT64 hwnd, Gdiplus::RectF rect)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}

	PREVIEW_PARAMS params{};
	g_sdkController->PreviewWindowOpenProjector("mobile_preview", videoModel, hwnd, rect.X, rect.Y, rect.Width, rect.Height, params);
}

void Controller::CloseMobilePreview(UINT32 videoModel)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	g_sdkController->PreviewWindowCloseProjector("mobile_preview", videoModel);
}

bool Controller::OpenCanvasPreview(const std::string& previewId, UINT32 videoModel, HWND hwnd, Gdiplus::RectF rect, const PREVIEW_PARAMS& params)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->PreviewWindowOpenProjector(previewId, videoModel, reinterpret_cast<uint64_t>(hwnd), rect.X, rect.Y, rect.Width, rect.Height, params);
}

bool Controller::CloseCanvasPreview(const std::string& previewId, UINT32 videoModel)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	return g_sdkController->PreviewWindowCloseProjector(previewId, videoModel);
}

bool Controller::SetCanvasPreviewParams(const std::string& previewID, UINT32 videoModel, const PREVIEW_PARAMS* params)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->SetProjectorWndParams(previewID, videoModel, *params);
}

bool Controller::SetCanvasPreviewPosition(const std::string& previewID, UINT32 videoModel, const Gdiplus::RectF& rect)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->SetProjectorPosition(previewID, videoModel, rect);
}

void Controller::SetDisplay(bool showView, UINT32 videoModel /*= UINT32_MAX*/)
{
	if (videoModel != UINT32_MAX)
	{
		UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
		if (mainSceneModel != UINT32_MAX)
		{
			videoModel = mainSceneModel;
		}

		g_mediaMgr->SetDisplay(videoModel, showView);
	}
	else
	{
        Mode* pMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
        if (pMode)
        {
            MODE_INFO_EX modeInfoEx{};
            pMode->GetModeInfo(&modeInfoEx);
            if (modeInfoEx.id == LIVE_MODE_LANDSCAPE)
            {
                g_mediaMgr->SetDisplay(0, showView);
            }
            else if (modeInfoEx.id == LIVE_MODE_PORTRAIT)
            {
                g_mediaMgr->SetDisplay(1, showView);
            }
            else if (modeInfoEx.id == LIVE_MODE_DBCANVAS)
            {
                for (int i = LIVE_MODE_DBCANVAS - 1; i >= 0 ; --i)
                {
                    g_mediaMgr->SetDisplay(i + 2, showView);
                }
            }
        }
	}
}

bool Controller::GetDisplay(UINT32 videoModel)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}

	return g_mediaMgr->GetDisplay(videoModel);
}

void Controller::SetDisplayMasks(const std::string& portraitFile, const std::string& landscapeFile)
{
    auto CleanupResources = [&](int canvasType) {
        auto it = m_canvasResources.find(canvasType);
        if (it != m_canvasResources.end())
        {
            auto& [layerID, sourceID] = it->second;
			if (ModeSceneMgr::GetInstance()->CheckLayerExist(layerID))
				ModeSceneMgr::GetInstance()->DeleteLayer(layerID);
			if (SourceMgr::GetInstance()->CheckSourceExist(sourceID))
				SourceMgr::GetInstance()->DestroySource(sourceID);
            m_canvasResources.erase(it);
        }
    };

    auto ProcessOrientation = [&](const std::string& file, int canvasType) {
        if (file.empty())
        {
            CleanupResources(canvasType);
            return;
        }

        MATERIAL_DESC mediaDesc{};
        if (!g_sdkController->GetMediaFileInfo(file, &mediaDesc))
            return;

        IMAGE_SOURCE image{};
        image.materialDesc = mediaDesc;

        SOURCE_INFO sourceInfo{};
        sourceInfo.type = VISUAL_IMAGE;
		sourceInfo.source = image;

		LAYER_INFO layerInfo{};
        layerInfo.transform.size = {mediaDesc.size.Width, mediaDesc.size.Height};

        std::string canvas_id = "";
        g_sdkController->GetCurrentCanvas(canvasType, &canvas_id);
        UINT64 canvasID = 0;
        Util::StringToNum(canvas_id, &canvasID);

        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
        {
            CANVAS_INFO_EX canvasInfoEx{};
            pCanvas->GetCanvasInfo(&canvasInfoEx);

            TRANSFORM transform{};
            transform.scale.X = canvasInfoEx.layoutRect.Width / layerInfo.transform.size.Width;
            transform.scale.Y = canvasInfoEx.layoutRect.Height / layerInfo.transform.size.Height;
            transform.size.Width = layerInfo.transform.size.Width;
			transform.size.Height = layerInfo.transform.size.Height;
            layerInfo.transform = transform;

            if (SourceMgr::GetInstance()->CreateSource(&sourceInfo, NULL))
			{
                const UINT64 sourceID = sourceInfo.id;
				layerInfo.sourceID = sourceID;
                if (ModeSceneMgr::GetInstance()->CreateLayer(&layerInfo, NULL))
				{
                    if (ModeSceneMgr::GetInstance()->BindCanvasLayer(canvasID, layerInfo.id))
                    {
                        std::string layer_id;
                        Util::NumToString(layerInfo.id, &layer_id);
						g_sdkController->LayerMoveTop(layer_id);
						g_sdkController->LayerSetLock(layer_id, true);
						g_sdkController->LayerSetFlag(layer_id, VISUAL_FLAG::VISUAL_FLAG_OUTPUT_FILTER);
						g_sdkController->LayerSetFlag(layer_id, VISUAL_FLAG::VISUAL_FLAG_ALWAYS_TOP);
						g_sdkController->LayerSetFlag(layer_id, VISUAL_FLAG::VISUAL_FLAG_AVOID_DESTROY_ALL);

                        m_canvasResources[canvasType] = {layerInfo.id, sourceID};
					}
					else
					{
						ModeSceneMgr::GetInstance()->DeleteLayer(layerInfo.id);
						SourceMgr::GetInstance()->DestroySource(sourceID);
					}
				}
				else
				{
					SourceMgr::GetInstance()->DestroySource(sourceID);
				}
			}
        }
    };

    ProcessOrientation(landscapeFile, 2);
    ProcessOrientation(portraitFile, 3);
}

void Controller::PlayAnimation(const std::string& portraitVideo, const std::string& landscapeVideo)
{
    return;
}

void Controller::EnableAllPreview(bool enable)
{
	g_mediaMgr->EnableAllPreview(enable);
}

void Controller::EnablePreviewByVideoModel(UINT32 videoModel, bool enable)
{
	g_mediaMgr->EnablePreviewByVideoModel(videoModel, enable);
}

void Controller::EnableInteract(bool enable)
{
	for (int i = 0; i < LIVE_MODE_DBCANVAS; ++i)
	{
		g_sdkController->PreviewWindowEnableInteract(i, enable);
	}
}

void Controller::CreateFullScreenDetector(const std::string& detectorID)
{
	g_mediaMgr->CreateFullScreenDetector(detectorID);
}

void Controller::DestroyFullScreenDetector(const std::string& detectorID)
{
	g_mediaMgr->DestroyFullScreenDetector(detectorID);
}

void Controller::SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exeNames)
{
	g_mediaMgr->SetFullScreenDetectorIgnoreProcessList(exeNames);
}

void Controller::GetFontFamilies(std::vector<std::string>* fonts)
{
	g_sdkController->TextSourceGetFonts(fonts);
}

void Controller::UpdateDynamicConfig(const std::string& json_config)
{
	g_sdkController->UpdateDynamicConfig(json_config);
}

void Controller::CheckEncoderSession(const std::string& name, UINT32 count, INT32* result)
{
	g_sdkController->CheckEncoderSession(name, count, result);
}

void Controller::StartColorPicker(HWND hwnd)
{
	g_mediaMgr->StartColorPicker(hwnd);
}

UINT64 Controller::AllocPreviewID()
{
	return ModeSceneMgr::GetInstance()->AllocPreviewID();
}

void Controller::InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect)
{
	g_mediaMgr->InitEffectPlatform(initEffect);
}

void Controller::UpdateEffectConfig(const std::string& userID, const std::string& ttlsHardwareLevel)
{
	g_mediaMgr->UpdateEffectConfig(userID, ttlsHardwareLevel);
}

void Controller::UnInitEffectPlatform()
{
	g_sdkController->EffectPlatformUninit();
}

void Controller::LoadEffectModels(const std::string& requestID, const std::string& modelName, const std::vector<std::string>& requirements)
{
	g_sdkController->EffectPlatformLoadModels(requestID, modelName, requirements);
}

void Controller::GetMediaFileInfo(const std::string& path, MATERIAL_DESC* desc)
{
	g_sdkController->GetMediaFileInfo(path, desc);
}

static bool ProcessImage(const std::wstring& inputPath,
                         const std::wstring& outputPath,
                         int32_t             raw_width,
                         int32_t             raw_height,
                         float               raw_scale_x,
                         float               raw_scale_y,
                         bool                flip_v,
                         bool                flip_h,
                         float               rotate,
                         int32_t             clip_x,
                         int32_t             clip_y,
                         int32_t             clip_z,
                         int32_t             clip_w)
{
    DirectX::ScratchImage originalImage;
    DirectX::TexMetadata  metadata;

    bool success = false;

    std::string errmsg;

    do
    {
        if (raw_width <= 0 || raw_height <= 0)
        {
            errmsg = "raw_width or raw_height <= 0";
            break;
        }

        if (raw_scale_x <= 0 || raw_scale_y <= 0)
        {
            errmsg = "raw_scale_x or raw_scale_y <= 0";
            break;
        }

        int32_t scaledWidth = (raw_width - clip_x - clip_z) * raw_scale_x;
        int32_t scaledHeight = (raw_height - clip_w - clip_y) * raw_scale_y;

        if (scaledWidth <= 0 || scaledHeight <= 0)
        {
            errmsg = "scaledWidth or scaledHeight <= 0";
            break;
        }

        HRESULT hr = DirectX::LoadFromWICFile(
            inputPath.c_str(),
            DirectX::WIC_FLAGS_NONE,
            &metadata,
            originalImage);

        if (FAILED(hr))
        {
            errmsg = "LoadFromWICFile fail";
            break;
        }

        int width = originalImage.GetMetadata().width;
        int height = originalImage.GetMetadata().height;

        if (width <= 0 || height <= 0)
        {
            errmsg = "originalImage width or height <= 0";
            break;
        }

        double scale_x = (double)width / raw_width;
        double scale_y = (double)height / raw_height;

        double scaled_clip_x = clip_x * scale_x;
        double scaled_clip_z = clip_z * scale_x;
        double scaled_clip_y = clip_y * scale_y;
        double scaled_clip_w = clip_w * scale_y;

        DirectX::Rect srcRect;
        srcRect.x = scaled_clip_x;
        srcRect.y = scaled_clip_y;
        srcRect.w = width - scaled_clip_z - scaled_clip_x;
        srcRect.h = height - scaled_clip_w - scaled_clip_y;

        if (srcRect.w <= 0 || srcRect.h <= 0 || srcRect.x > width || srcRect.y > height)
        {
            srcRect.x = 0;
            srcRect.y = 0;
            srcRect.w = width;
            srcRect.h = height;
        }

        DirectX::ScratchImage croppedImage;

        hr = croppedImage.Initialize2D(
            metadata.format,
            srcRect.w,
            srcRect.h,
            1, 1, DirectX::CP_FLAGS_NONE);
        if (FAILED(hr))
        {
            errmsg = "croppedImage.Initialize2D fail";
            break;
        }

        hr = DirectX::CopyRectangle(*originalImage.GetImages(), srcRect, *croppedImage.GetImages(), DirectX::TEX_FILTER_DEFAULT, 0, 0);
        if (FAILED(hr))
        {
            errmsg = "CopyRectangle fail";
            break;
        }

        DirectX::ScratchImage resizedImage;

        hr = DirectX::Resize(*croppedImage.GetImages(), scaledWidth, scaledHeight, DirectX::TEX_FILTER_DEFAULT, resizedImage);
        if (FAILED(hr))
        {
            errmsg = "Resize fail";
            break;
        }

        DirectX::TEX_FR_FLAGS flag = DirectX::TEX_FR_ROTATE0;

        rotate = std::fmod(rotate, 360.0);
        if (rotate < 0)
            rotate += 360.0;

        if (rotate > 89 && rotate < 91)
            flag = DirectX::TEX_FR_ROTATE90;
        else if (rotate > 179 && rotate < 181)
            flag = DirectX::TEX_FR_ROTATE180;
        else if (rotate > 269 && rotate < 271)
            flag = DirectX::TEX_FR_ROTATE270;

        if (flip_v)
            flag |= DirectX::TEX_FR_FLIP_VERTICAL;

        if (flip_h)
            flag |= DirectX::TEX_FR_FLIP_HORIZONTAL;

        DirectX::ScratchImage flippedImage;

        hr = DirectX::FlipRotate(
            resizedImage.GetImages(),
            resizedImage.GetImageCount(),
            resizedImage.GetMetadata(),
            flag,
            flippedImage);

        if (FAILED(hr))
        {
            errmsg = "FlipRotate fail";
            break;
        }

        hr = DirectX::SaveToWICFile(
            flippedImage.GetImages(),
            flippedImage.GetImageCount(),
            DirectX::WIC_FLAGS_NONE,
            DirectX::GetWICCodec(DirectX::WIC_CODEC_PNG),
            outputPath.c_str());

        if (FAILED(hr))
        {
            errmsg = "SaveToWICFile fail";
            break;
        }

        success = true;

    } while (0);

    if (!success)
    {
        LOG(INFO) << StringPrintf("[Controller::ProcessImage] success:0, errmsg:%s", errmsg.c_str());
    }

    return success;
}

void Controller::GetLayerSnapshot(UINT64 layerID, const std::string& path)
{
	std::string layerIDStr = "";
	Util::NumToString(layerID, &layerIDStr);
	g_sdkController->VisualSaveAsImage(layerIDStr, path);
}

bool Controller::GetLayerSnapshot2(UINT64 layerID, const std::string& path)
{
    std::string layerIDStr = "";
    Util::NumToString(layerID, &layerIDStr);

    bool success = false;

    std::wstring wtmpFile;
    std::string  tmpFile;

    do
    {
        bool exist = this->FindLayerByID(layerID);
        if (!exist)
        {
            LOG(INFO) << StringPrintf("[Controller::GetLayerSnapshot2] LayerId:%llu not existed", layerID);
            break;
        }

        LAYER_INFO layerInfo;
        GetLayerInfo(layerID, &layerInfo);

        std::error_code ec;
        auto            tmpFolderPath = std::filesystem::temp_directory_path(ec);
        if (ec)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] temp_directory_path fail";
            break;
        }

        static std::atomic_int64_t g_count = 0;
        ++g_count;

        std::string tmpFilename = StringPrintf("GetLayerSnapshot2_%lld.png", g_count.load());
        tmpFolderPath.append(tmpFilename);

        tmpFile = tmpFolderPath.string();
        wtmpFile = tmpFolderPath.wstring();

        bool ok = g_sdkController->VisualSaveAsImage(layerIDStr, tmpFile.c_str());
        if (!ok)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] VisualSaveAsImage fail";
            break;
        }

        std::wstring dstPath;

        Util::UTF8ToWString(path.c_str(), dstPath);

        /*ok = ProcessImage(wtmpFile,
                          dstPath,
                          layerInfo.transform.size.Width,
                          layerInfo.transform.size.Height,
                          layerInfo.transform.scale.X,
                          layerInfo.transform.scale.Y,
                          layerInfo.transform.vFlip,
                          layerInfo.transform.hFlip,
                          layerInfo.transform.angle,
                          layerInfo.transform.clipRange.x,
                          layerInfo.transform.clipRange.y,
                          layerInfo.transform.clipRange.z,
                          layerInfo.transform.clipRange.w);*/

        ok = ProcessImage(wtmpFile,
                          dstPath,
                          layerInfo.transform.size.Width,
                          layerInfo.transform.size.Height,
                          layerInfo.transform.scale.X,
                          layerInfo.transform.scale.Y,
                          layerInfo.transform.vFlip,
                          layerInfo.transform.hFlip,
                          layerInfo.transform.angle,
                          0,
                          0,
                          0,
                          0);

        if (!ok)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] ProcessImage fail";
            break;
        }

        success = true;

    } while (0);

    if (!wtmpFile.empty())
    {
        std::error_code ec;
        std::filesystem::remove(wtmpFile, ec);
    }

    return success;
}

void Controller::GetEffectProfiler(UINT64 layerID, UINT64 sourceID, EffectProfilerInfo* effectProfilerInfo)
{
	std::string source_id = "";
	Util::NumToString(sourceID, &source_id);
	if (SourceMgr::GetInstance()->GetCompositeRealType(sourceID) == VISUAL_IMAGE)
		return;

	std::string layer_id = "";
	Util::NumToString(layerID, &layer_id);
	g_sdkController->VisualGetEffectProfiler(layer_id, effectProfilerInfo);
}

void Controller::UpdateLayerSource(UINT64 layerID, UINT64 sourceID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
		ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);
		
		if (layerInfo.sourceID == sourceID)
		{
			LOG(INFO) << "[Controller::UpdateLayerSource] update layer source is same, newSourceID: " << sourceID << ", oldSourceID: " << layerInfo.sourceID;
			return;
		}

		if (SourceMgr::GetInstance()->CheckSourceExist(sourceID))
			SourceMgr::GetInstance()->TransferLayerBindingsWithTransform(layerInfo.sourceID, sourceID);
	}
}

bool Controller::CreateFrame(UINT64 mediaID, Gdiplus::SizeF resize, CLIPF clip, OBJECT_FIT_MODE fitMode, UINT64* frameID)
{
    std::string mediaIDStr = "";
    Util::NumToString(mediaID, &mediaIDStr);

    *frameID = ModeSceneMgr::GetInstance()->AllocFrameID();
    std::string frameIDStr = "";
    Util::NumToString(*frameID, &frameIDStr);
	if (!g_videoFrameProcessor->GrabFrame(mediaIDStr, frameIDStr, v3::ClipResizeOrderEnum::kClipResizeOrder_ClipFirst, static_cast<v3::FitModeEnum>(fitMode), resize.Width, resize.Height, clip.x, clip.y, clip.z, clip.w))
	{
        LOG(ERROR) << "[Controller::CreateFrame] GrabFrame failed";
        return false;
	}

    return true;
}

void Controller::HandleLayerSizeChange(UINT64 layerID)
{
    LAYER_INFO layerInfo{};
    GetLayerInfo(layerID, &layerInfo);
    SOURCE_INFO sourceInfo{};
    GetSourceInfo(layerInfo.sourceID, &sourceInfo);
    ModeSceneMgr::GetInstance()->HandleLayerSizeChange(layerID, sourceInfo.size.Width, sourceInfo.size.Height);
}

bool Controller::ResetParfait(PARFAIT_PARAM param)
{
	g_sdkController->UpdateHost(param.host);
	return LogDumpMgr::GetInstance()->ResetParfait(param);
}

void Controller::SetParfaitContextInfo(const std::string& key, const std::string& val)
{
	LogDumpMgr::GetInstance()->SetParfaitContextInfo(key, val);
}

bool Controller::StartWatchdog(const char* pipeName, int timeout)
{
	if (WatchdogClient::GetInstance().IsRunning())
		return false;

	WatchdogClient::GetInstance().RegisterThread(THREADNAME_RENDER);
	WatchdogClient::GetInstance().RegisterThread(THREADNAME_BACK);
	WatchdogClient::GetInstance().SetTimeout(timeout);
	WatchdogClient::GetInstance().SetPipeName(StringToWString(pipeName, CP_UTF8));
	WatchdogClient::GetInstance().Create();
	return true;
}

void Controller::StopWatchdog()
{
	WatchdogClient::GetInstance().Destroy();
}

void Controller::EnableFrozenMonitor(int timeout)
{
	IThreadMonitor* threadMonitor = (IThreadMonitor*)g_cief->QueryInterface(LSINAME_THREADMONITOR);
	if (threadMonitor)
	{
		UINT64 timeoutu64 = timeout;
		threadMonitor->SetFrozenTime(timeoutu64);
	}
}

void Controller::ServerCrash()
{
	int a = 0;
	LOG(INFO) << StringPrintf("Server crash");
	LOG(INFO) << StringPrintf("%d", 10 / a);
}

void Controller::MockFrozen(int ipcTimeout, int renderTimeout)
{
	if (ipcTimeout > 0)
	{
		LOG(INFO) << "Mock ipc timeout[" << ipcTimeout << "]";
		std::this_thread::sleep_for(std::chrono::milliseconds(ipcTimeout));
	}
	if (renderTimeout > 0)
	{
		LOG(INFO) << "Mock render timeout[" << ipcTimeout << "]";
		g_sdkController->MockFrozen(ipcTimeout, renderTimeout);
	}
}

bool Controller::SelectMode(LIVE_MODE mode)
{
	bool success = ModeSceneMgr::GetInstance()->SelectMode(mode);
	return success;
}

UINT64 Controller::AddSceneWithInfo(LIVE_MODE mode, SCENE_INFO* sceneInfo)
{
	//用场景的原子操作完成UI上的添加场景，CreateScene,UpdateSize,SwitchIn. 
	//这里只创建新场景，不切入。
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(mode);
	if (pMode)
	{
		return pMode->AddSceneWithInfo(sceneInfo);
	}
	return 0;
}

void Controller::AddSceneByID(LIVE_MODE mode, UINT64 sceneID)
{
	return ModeSceneMgr::GetInstance()->BindModeScene(mode, sceneID);
}

void Controller::RemoveScene(LIVE_MODE mode, UINT64 sceneID)
{
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(mode);
	if (pMode)
	{
		Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
		pMode->RemoveScene(pScene);
	}
}

UINT64 Controller::CreateScene(SCENE_INFO* sceneInfo)
{
	return ModeSceneMgr::GetInstance()->CreateScene(sceneInfo, NULL, &sceneInfo->mode);
}

void Controller::DeleteScene(UINT64 sceneID)
{
	ModeSceneMgr::GetInstance()->DeleteScene(sceneID);
}

bool Controller::FindSceneByID(UINT64 sceneID)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!pScene)
		return false;
	return true;
}

void Controller::SelectScene(UINT64 sceneID, bool reloadWhenSwitchMode)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!pScene)
		return;
	SCENE_INFO_EX sceneInfo = {};
	pScene->GetSceneInfo(&sceneInfo);
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(sceneInfo.mode);
	if (!pMode)
		return;

	pMode->SelectScene(sceneID, reloadWhenSwitchMode);
}

void Controller::GetSceneInfo(UINT64 sceneID, SCENE_INFO* info)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		SCENE_INFO_EX infoEx = {};
		pScene->GetSceneInfo(&infoEx);
		*info = *(SCENE_INFO*)&infoEx;
	}
}

void Controller::PreLoadScene(UINT64 sceneID, bool preloadWhenSwitchMode)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		if (preloadWhenSwitchMode)
		{
			pScene->Select(true, SWITCH_SCENE_REPRELOAD);
		}
		else
		{
			pScene->Select(true, SWITCH_SCENE_PRELOAD);
		}
	}
}

UINT64 Controller::CreateCanvas(CANVAS_INFO* info)
{
	return ModeSceneMgr::GetInstance()->CreateCanvas(info, 0);
}

UINT64 Controller::GetCanvasID(UINT64 sceneID, UINT32 videoModel)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		SCENE_INFO_EX sceneInfoEx{};
        pScene->GetSceneInfo(&sceneInfoEx);
		if (sceneInfoEx.mode == LIVE_MODE_LANDSCAPE)
		{
			videoModel = 0;
		}
		else if (sceneInfoEx.mode == LIVE_MODE_PORTRAIT)
		{
			videoModel = 1;
		}

		Canvas* pCanvas = pScene->GetCanvas((LIVE_MODE)videoModel);
		if (pCanvas)
		{
			CANVAS_INFO_EX canvasInfo;
			pCanvas->GetCanvasInfo(&canvasInfo);
			return canvasInfo.id;
		}
	}
	return 0;
}

bool Controller::DestroyCanvas(UINT64 canvasID)
{
	ModeSceneMgr::GetInstance()->DeleteCanvas(canvasID);

	std::string canvasIDStr = "";
	Util::NumToString(canvasID, &canvasIDStr);
	return g_sdkController->DestoryCanvas(canvasIDStr);
}

void Controller::PreviewCanvas(UINT64 canvasID, CANVAS_INFO* info)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && info)
	{
		pCanvas->SetCanvasInfo(info);
		pCanvas->Select(true);
		g_mediaMgr->ShowModel(info->videoModel, true);
	}
}

void Controller::DestroyPreview(UINT64 canvasID)
{
	if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
	{
		CANVAS_INFO_EX canvasInfoEx{};
		pCanvas->GetCanvasInfo(&canvasInfoEx);
		UINT32 videoModel = canvasInfoEx.videoModel;
		DestroyCanvas(canvasID);
        g_sdkController->RemoveModel(videoModel);
	}
}

UINT64 Controller::GetCanvasIDByPreviewID(UINT64 previewID)
{
	return m_previewIDMapCanvasID[previewID];
}

void Controller::SetPreviewIDMapCanvasID(UINT64 previewID, UINT64 canvasID)
{
	m_previewIDMapCanvasID[previewID] = canvasID;
}

void Controller::UpdateCanvasLayout(UINT64 canvasID, CANVAS_INFO* info)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && info)
		pCanvas->UpdateCanvasLayout(info);
}

void Controller::GetCanvasInfo(UINT64 canvasID, CANVAS_INFO* info)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && info)
	{
		CANVAS_INFO_EX infoEx = {};
		pCanvas->GetCanvasInfo(&infoEx);
		*info = *(CANVAS_INFO*)&infoEx;
	}
}

void Controller::SetCanvasInfo(UINT64 canvasID, const CANVAS_INFO* info)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
	{
		pCanvas->SetCanvasInfo(info);
	}
}

void Controller::EnumLayers(UINT64 canvasID, CANVAS_INFO_EX* infoEx)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && infoEx)
	{
		pCanvas->GetCanvasInfo(infoEx, true);
	}
}

bool Controller::FindCanvasByID(UINT64 canvasID)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (!pCanvas)
		return false;

	return true;
}

bool Controller::CheckCanvasByID(UINT64 canvasID)
{
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (!pCanvas)
        return false;

    CANVAS_INFO_EX canvasInfoEx{};
    pCanvas->GetCanvasInfo(&canvasInfoEx);
    return canvasInfoEx.isCreated;
}

void Controller::UpdateLayersOrder(UINT64 canvasID, const std::vector<UINT64>& layerIDs, std::vector<UINT64>* moreLayerIDs, std::vector<UINT64>* lessLayerIDs)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
		pCanvas->UpdateWholeLayersOrder(layerIDs, moreLayerIDs, lessLayerIDs);
}

void Controller::MoveLayerOrder(UINT64 canvasID, UINT64 layerID, MOVE_ORDER move)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
		pCanvas->MoveLayerOrder(layerID, move);
}

UINT64 Controller::AddLayerWithInfo(UINT64 canvasID, LAYER_INFO* layerInfo)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && layerInfo)
		return pCanvas->AddLayerWithInfo(layerInfo, true, 0);
	return 0;
}

bool Controller::AddLayerByID(UINT64 canvasID, UINT64 layerID)
{
	bool success = ModeSceneMgr::GetInstance()->BindCanvasLayer(canvasID, layerID);
	return success;
}

void Controller::RemoveLayer(UINT64 canvasID, UINT64 layerID)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
	{
		Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
		pCanvas->RemoveLayer(pLayer);
    }
}

bool Controller::CreateLayer(LAYER_INFO* layerInfo)
{
	layerInfo->id = ModeSceneMgr::GetInstance()->CreateLayer(layerInfo, NULL);
	return layerInfo->id > 0;
}

void Controller::AddBrowserLayer(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo)
{
    if (sourceInfo && SourceMgr::GetInstance()->FindSourceByID(sourceInfo->id))
    {
        ModeSceneMgr::GetInstance()->OnBrowserSourceUpdate(layerInfo, sourceInfo);
    }
}

void Controller::DeleteLayer(UINT64 layerID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (!pLayer)
		return;

	ModeSceneMgr::GetInstance()->DeleteLayer(layerID);
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(layerID);
    if (pAudio)
        AudioMgr::GetInstance()->DestoryAudio(pAudio);
}

void Controller::ControlLayer(UINT64 layerID, LAYER_INFO& layerInfo, LAYER_CONTROL_CMD cmd)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
        if (!(cmd & LAYER_CONTROL_SET_LAYOUT) && (cmd & LAYER_CONTROL_SET_ROTATE || cmd & LAYER_CONTROL_SET_TRANSLATE || cmd & LAYER_CONTROL_SET_SCALE))
        {
            layerInfo.layout = LAYOUT_NONE;
        }
        pLayer->ControlLayer(layerInfo, cmd);

		Source* pSource = SourceMgr::GetInstance()->GetSourceByID(layerInfo.sourceID);
		if (pSource)
		{
            SOURCE_INFO sourceInfo{};
            pSource->GetSourceInfo(&sourceInfo);
            if (sourceInfo.type == VISUAL_FAV && (cmd & LAYER_CONTROL_SET_SHOW))
            {
                FAV_SOURCE fav = std::get<FAV_SOURCE>(sourceInfo.source);
                fav.seekTime = .0f;
                fav.absolute = true;
                sourceInfo.source = fav;
                UINT64 sourceCMD = SOURCE_CONTROL_SEEK;
				pSource->ControlSource(sourceInfo, static_cast<SOURCE_CONTROL_CMD>(sourceCMD));
            }
		}
	}
}

void Controller::GetLayerInfo(UINT64 layerID, LAYER_INFO* layerInfo)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if(pLayer && layerInfo)
		pLayer->GetLayerInfo(layerInfo);
}

void Controller::SetLayerInfo(UINT64 layerID, const LAYER_INFO& layerInfo)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
		pLayer->SetLayerInfo(&layerInfo);
}

bool Controller::FindLayerByID(UINT64 layerID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (!pLayer)
		return false;
	return true;
}

void Controller::ReopenLayer(UINT64 canvasID, LAYER_INFO* layerInfo)
{
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (pCanvas && layerInfo)
    {
        if (FindLayerByID(layerInfo->id))
        {
			std::vector<UINT64> eOrder{};
            std::vector<UINT64> order{};
            pCanvas->GetLayersOrder(&order);
			
			ModeSceneMgr::GetInstance()->DeleteLayer(layerInfo->id);
			pCanvas->AddLayerWithInfo(layerInfo, true, &layerInfo->id);
			UpdateLayersOrder(canvasID, order, NULL, NULL);
        }
    }
}

void Controller::UnSelectLayer()
{
	g_sdkController->SelectLayer("", "");
}

void Controller::RemoveGraffiti(UINT64 sourceID)
{
	std::string sourceIDStr = "";
	Util::NumToString(sourceID, &sourceIDStr);
	g_sdkController->GraffitiSourceRemove(sourceIDStr);
}

void Controller::RemoveAllGraffiti(UINT64 sourceID, bool enable)
{
	std::string sourceIDStr = "";
	Util::NumToString(sourceID, &sourceIDStr);
	g_sdkController->GraffitiSourceRemoveAll(sourceIDStr, enable);
}

bool Controller::StartLayerPreview(UINT64 layerID, UINT64 parent, const LAYER_PREVIEW* preview, UINT64* previewID, const PREVIEW_PARAMS& params)
{
	if (previewID)
	{
        *previewID = ModeSceneMgr::GetInstance()->AllocPreviewID();
        Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
        if (pLayer)
        {
            pLayer->SetLayerPreview(*previewID, preview);
        }
	}

	std::string layerIDStr = "";
	Util::NumToString(layerID, &layerIDStr);

	std::string previewIDStr = "";
	Util::NumToString(*previewID, &previewIDStr);
	return g_sdkController->StartLayerPreview(layerIDStr, previewIDStr, parent, *preview, params);
}

bool Controller::StopLayerPreview(UINT64 layerID, UINT64 previewID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_PREVIEW preview{};
		pLayer->SetLayerPreview(previewID, &preview);
	}

	std::string previewIDStr = "";
	Util::NumToString(previewID, &previewIDStr);
	return g_sdkController->StopLayerPreview(previewIDStr);
}

bool Controller::SetLayerPreviewLayout(UINT64 layerID, UINT64 previewID, const LAYER_PREVIEW* preview)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		pLayer->SetLayerPreview(previewID, preview);
	}

	std::string previewIDStr = "";
	Util::NumToString(previewID, &previewIDStr);
	return g_sdkController->SetLayerPreviewSetting(previewIDStr, *preview);
}

void Controller::GetLayerPreview(UINT64 layerID, UINT64 previewID, LAYER_PREVIEW* preview)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
		pLayer->GetLayerPreview(previewID, preview);
}

bool Controller::SetLayerPreviewParams(const std::string& layerID, const std::string& previewID, const PREVIEW_PARAMS* params)
{
	return g_sdkController->SetLayerPreviewParams(previewID, *params);
}

bool Controller::CreateSource(SOURCE_INFO& sourceInfo)
{
	if (SourceMgr::GetInstance()->FindSourceByID(sourceInfo.id))
		return true;
	
	return SourceMgr::GetInstance()->CreateSource(&sourceInfo, NULL) > 0;
}

bool Controller::PauseSource(const std::string& sourceID)
{
	return g_sdkController->MediaSourcePause(sourceID);
}

bool Controller::ResumeSource(const string& sourceID)
{
	return g_sdkController->MediaSourceResume(sourceID);
}

bool Controller::ReopenSource(UINT64 sourceID, SOURCE_INFO& sourceInfo)
{
	return SourceMgr::GetInstance()->ReopenSource(sourceID, &sourceInfo);
}

void Controller::DestroySource(UINT64 sourceID)
{
	SourceMgr::GetInstance()->DestroySource(sourceID);
}

void Controller::ControlSource(UINT64 sourceID, SOURCE_INFO& sourceInfo, SOURCE_CONTROL_CMD cmd)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->ControlSource(sourceInfo, cmd);
	}
}

void Controller::SetSourceInfo(UINT64 sourceID, const SOURCE_INFO* sourceInfo)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->SetSourceInfo(sourceInfo);
	}
}

bool Controller::FindSourceByID(UINT64 sourceID)
{
    return SourceMgr::GetInstance()->FindSourceByID(sourceID);
}

void Controller::GetSourceInfo(UINT64 sourceID, SOURCE_INFO* info, SOURCE_INFO_CMD cmd /*= SOURCE_INFO_NONE*/)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->GetSourceInfo(info, cmd);
	}
}

std::unordered_map<UINT64, COMPOSITE_SOURCE_META> Controller::GetCompositeMetas()
{
	return SourceMgr::GetInstance()->GetCompositeMetas();
}

bool Controller::IsMatchedSource(SOURCE_INFO& sourceInfo)
{
	return SourceMgr::GetInstance()->IsMatchedSource(sourceInfo);
}

void Controller::SetCompositeMetas(const std::unordered_map<UINT64, COMPOSITE_SOURCE_META>& metas)
{
	SourceMgr::GetInstance()->SetCompositeMetas(metas);
}

void Controller::EnumAppAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->AppAudioSourceEnum(devices);
}

void Controller::EnumCaptureAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->WASAPIAudioSourceEnumInputDevices(devices);
}

void Controller::EnumRenderAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->WASAPIAudioSourceEnumOutputDevices(devices);
}

void Controller::GetDefaultInput(DSHOW* device)
{
	g_sdkController->WASAPIAudioSourceGetDefaultInputDevice(device);
}

void Controller::GetDefaultOutput(DSHOW* device)
{
	g_sdkController->WASAPIAudioSourceGetDefaultOutputDevice(device);
}

void Controller::SystemSupportAppAudio(bool* support)
{
	g_sdkController->AppAudioSourceIsSystemSupport(support);
}

UINT64 Controller::AddAudio(AUDIO_INFO* audioInfo)
{
	if (audioInfo->type == AUDIO_VIS)
	{
		if (FindLayerByID(audioInfo->id))
			return AudioMgr::GetInstance()->CreateAudio(audioInfo, &audioInfo->id);
	}
	else
	{
		return AudioMgr::GetInstance()->CreateAudio(audioInfo);
	}

	return 0;
}

void Controller::DeleteAudio(UINT64 audioID)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (pAudio)
		AudioMgr::GetInstance()->DestoryAudio(pAudio);
}

bool Controller::ControlAudio(UINT64 audioID, AUDIO_CONTROL_INFO* info)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(info->audioInfo.id);
    if (!pAudio)
        return false;

    if (!pAudio->ControlAudio(info))
        return false;

	AUDIO_INFO audioInfo{};
	pAudio->GetAudioInfo(&audioInfo);
	if (audioInfo.eraseAudioID > 0)
	{
		if (EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(audioInfo.eraseAudioID))
		{
			ERASE_AUDIO_INFO eraseAudioInfo{};
			pEraseAudio->GetEraseAudioInfo(&eraseAudioInfo);

			UINT64 cmd = AUDIO_CONTROL_NONE;
			if (info->cmd & AUDIO_CONTROL_SET_SYNC_OFFSET)
			{
				cmd |= AUDIO_CONTROL_SET_SYNC_OFFSET;
			}
			if (info->cmd & AUDIO_CONTROL_SET_AUDIO_TRACK)
			{
				cmd |= AUDIO_CONTROL_SET_AUDIO_TRACK;
				if (audioInfo.audioTrack == 0)
				{
					eraseAudioInfo.audioTrack = 0;
				}
				else
				{
					eraseAudioInfo.audioTrack = ERASE_AUDIO_TRACK_0;
				}

				if (!ChangeAudioTrack(audioID, ERASE_AUDIO_TRACK_0, ERASE_AUDIO_TRACK_5))
					LOG(WARNING) << "[Controller::ControlAudio] ChangeAudioTrack audioID: " << audioID << " AUDIO_TRACK_0 to AUDIO_TRACK_5 failed";
			}

			if (cmd != AUDIO_CONTROL_NONE)
			{
				eraseAudioInfo.audioSetting = audioInfo.audioSetting;
				info->eraseAudioInfo = eraseAudioInfo;
				info->cmd = static_cast<AUDIO_CONTROL_CMD>(cmd);
				pEraseAudio->ControlEraseAudio(info);
			}
		}
	}

	return true;
}

void Controller::SetRenderDeviceID(const std::string& deviceID)
{
	g_sdkController->AudioSourceSetRenderDeviceID(deviceID);
}

bool Controller::SetANSOption(const AUDIO_ANS_OPTION& ansOption)
{
	return g_sdkController->LyraxEngineSetANSOption("", ansOption);
}

void Controller::GetAudioInfo(UINT64 audioID, AUDIO_INFO* info, AUDIO_INFO_CMD cmd)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (pAudio)
		pAudio->GetAudioInfo(info, cmd);
}

bool Controller::FindAudioByID(UINT64 audioID)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (!pAudio)
		return false;
	return true;
}

bool Controller::StartListenAudio(const std::string& audioID)
{
	if (!g_audioFrameProcessor->AudioInputStartListen(audioID, v3::AudioFrameProcessor::AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw))
	{
		LOG(ERROR) << "[Controller::StartListenAudio] AudioInputStartListen failed, audioID: " << audioID;
		return false;
	}

	return true;
}

bool Controller::StopListenAudio(const std::string& audioID)
{
	if (!g_audioFrameProcessor->AudioInputStopListen(audioID))
	{
        LOG(ERROR) << "[Controller::StopListenAudio] AudioInputStopListen failed, audioID: " << audioID;
        return false;
	}

	return true;
}

bool Controller::ChangeAudioTrack(UINT64 audioID, UINT32 oriTrack, UINT32 tarTrack)
{
	if (audioID == 0)
		return true;
	
	Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
	if (!pAudio)
	{
		LOG(ERROR) << "[Controller::ChangeAudioTrack] audio is invalid, audioID: " << audioID;
		return false;
	}

	AUDIO_INFO audioInfo{};
	pAudio->GetAudioInfo(&audioInfo);

	if ((audioInfo.audioTrack & oriTrack) != 0)
	{
		audioInfo.audioTrack = (audioInfo.audioTrack & ~oriTrack) | tarTrack;

		AUDIO_CONTROL_INFO audioControlInfo{};
		audioControlInfo.audioInfo = audioInfo;
		audioControlInfo.cmd = AUDIO_CONTROL_SET_AUDIO_TRACK;
		return pAudio->ControlAudio(&audioControlInfo);
	}

	return true;
}

UINT64 Controller::CreateEraseAudio(const ERASE_AUDIO_INFO* eraseAudioInfo)
{
    UINT64 eraseAudioID = AudioMgr::GetInstance()->CreateEraseAudio(eraseAudioInfo);
	if (eraseAudioID > 0)
	{
		if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(eraseAudioInfo->originAudioID))
		{
			AUDIO_INFO audioInfo{};
			pAudio->GetAudioInfo(&audioInfo);
			audioInfo.eraseAudioID = eraseAudioID;
			pAudio->SetAudioInfo(&audioInfo);
		}
		
		return eraseAudioID;
	}

	return 0;
}

bool Controller::DeleteEraseAudio(UINT64 eraseAudioID)
{
	if (EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID))
	{
		ERASE_AUDIO_INFO eraseAudioInfo{};
		pEraseAudio->GetEraseAudioInfo(&eraseAudioInfo);

		if (AudioMgr::GetInstance()->DestroyEraseAudio(pEraseAudio))
		{
			if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(eraseAudioInfo.originAudioID))
			{
				AUDIO_INFO audioInfo{};
				pAudio->GetAudioInfo(&audioInfo);
				audioInfo.eraseAudioID = 0;
				pAudio->SetAudioInfo(&audioInfo);
			}

			return true;
		}
	}

	return false;
}

bool Controller::FindEraseAudioByID(UINT64 eraseAudioID)
{
	return AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID) != nullptr;
}

void Controller::GetEraseAudioInfo(UINT64 eraseAudioID, ERASE_AUDIO_INFO* info)
{
	EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID);
	if (pEraseAudio)
		pEraseAudio->GetEraseAudioInfo(info);
}

bool Controller::EraseAudioEnableAutoMute(UINT64 eraseAudioID, bool enable)
{
	if (EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID))
	{
		return pEraseAudio->EnableAutoMute(eraseAudioID, enable);
	}
	return false;
}

bool Controller::EraseAudioSetOriginAudio(UINT64 eraseAudioID, UINT64 originAudioID)
{
	if (EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID))
	{
		if (pEraseAudio->SetOriginAudio(originAudioID))
		{
			if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(originAudioID))
			{
				AUDIO_INFO audioInfo{};
				pAudio->GetAudioInfo(&audioInfo);
				audioInfo.eraseAudioID = eraseAudioID;
				pAudio->SetAudioInfo(&audioInfo);
			}

			return true;
		}
	}

	return false;
}

bool Controller::EraseAudioSetEraseRange(UINT64 eraseAudioID, INT64 beginPTSMs, INT64 endPTSMs, bool* hit, INT64* bufferBeginPTSMs, INT64* bufferEndPTSMs)
{
	if (EraseAudio* pEraseAudio = AudioMgr::GetInstance()->GetEraseAudioByID(eraseAudioID))
	{
		return pEraseAudio->SetEraseRange(eraseAudioID, beginPTSMs, endPTSMs, hit, bufferBeginPTSMs, bufferEndPTSMs);
	}

	return false;
}

bool Controller::EnableAudioInputEchoDetection(UINT64 audioID, const int interval)
{
    std::string audio_id = "";
    Util::NumToString(audioID, &audio_id);
    return g_sdkController->LyraxEngineEnableAudioInputEchoDetection(audio_id, interval);
    
}

bool Controller::SetAudioInputRenderDeviceID(UINT64 audioID, const std::string& deviceID)
{
    std::string audio_id = "";
    Util::NumToString(audioID, &audio_id);
    return g_sdkController->LyraxEngineSetAudioInputRenderDeviceID(audio_id, deviceID.c_str());
}

void Controller::AddFilter(UINT64 mediaID, UINT64 filterID)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
	{
		FILTER info;
		pFilter->GetFilterInfo(&info);
		if (info.type == FILTER_VISUAL || info.type == FILTER_EFFECT)
		{
			Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
			if (pLayer)
				pLayer->AddFilter(mediaID, &info);
		}
		else if (info.type == FILTER_AUDIO)
		{
			Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
			if (pAudio)
				pAudio->AddFilter(mediaID, &info);
		}
		else if (info.type == FILTER_TRANSITION)
		{
			Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
			if (pCanvas)
				pCanvas->AddFilter(mediaID, &info);
		}
	}
}

void Controller::RemoveFilter(UINT64 mediaID, UINT64 filterID)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
	{
		FILTER info;
		pFilter->GetFilterInfo(&info);
		if (info.type == FILTER_VISUAL || info.type == FILTER_EFFECT)
		{
			Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
			if (pLayer)
				pLayer->RemoveFilter(mediaID, &info);
		}
		else if (info.type == FILTER_AUDIO)
		{
			Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
			if (pAudio)
				pAudio->RemoveFilter(mediaID, &info);
		}
		else if (info.type == FILTER_TRANSITION)
		{
			Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
			if (pCanvas)
			{
				pCanvas->RemoveFilter(mediaID, &info);
			}
		}
	}
}

void Controller::ResetFilterOrder(UINT64 layerID, const std::vector<std::string>& filterIDs)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
	    pLayer->GetLayerInfo(&layerInfo);

		std::string layerIDStr = "";
		Util::NumToString(layerID, &layerIDStr);

		std::vector<std::string> convertedFilterIDs{};
		for (const auto filterID : filterIDs)
		{
			auto it = std::find_if(layerInfo.filters.begin(), layerInfo.filters.end(), [filterID](FILTER filterInfo) {
				UINT64 id = 0;
				bool success = Util::StringToNum(filterID, &id);
				if (!success)
				{
					LOG(ERROR) << "[Controller::ResetFilterOrder] Util::StringToNum failed, filterID in: " << filterID << ", out: " << id;
					return false;
				}
				return filterInfo.id == id;
			});

			if (it != layerInfo.filters.end())
			{
				if (it->type == FILTER_VISUAL)
				{
                    auto filter = std::get<VISUAL_FILTER>(it->filter);
                    if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                    {
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_chromakey");
                    }
					else if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
					{
					    convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_coloradjust");
					}
					else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
					{
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_color_lut");
					}
					else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
					{
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_sharpness");
					}
					else
					{
					    convertedFilterIDs.push_back(layerIDStr + "_" + filterID);
					}
				}
				else if (it->type == FILTER_EFFECT)
				{
				    convertedFilterIDs.push_back(layerIDStr + "_filter");
				}
			}
		}
		g_sdkController->ResetFilterOrder(layerIDStr, convertedFilterIDs);

		layerInfo.filters = ResetLayerFilters(layerInfo.filters, filterIDs);
		pLayer->SetLayerInfo(&layerInfo);
	}
}

std::vector<FILTER> Controller::ResetLayerFilters(std::vector<FILTER> filters, std::vector<std::string> filterIDs)
{
	std::vector<FILTER> result{};
	std::vector<FILTER> reOrder{};
	std::unordered_set<std::string> filterIDSet(filterIDs.begin(), filterIDs.end());

	for (const auto& filter : filters)
	{
		std::string filterIDStr = "";
		bool ret = Util::NumToString(filter.id, &filterIDStr);
		if (!ret)
		{
			LOG(ERROR) << "[Controller::ResetLayerFilters] til::StringToNum failed, filters filterID in: " << filter.id << ", out: " << filterIDStr;
			continue;
		}

		if (filterIDSet.count(filterIDStr))
		{
			reOrder.push_back(filter);
		}
		else
		{
			result.push_back(filter);
		}
	}

	int insertPos = 0;
	for (const std::string& filterID : filterIDs)
	{
		for (int i = 0; i < reOrder.size(); ++i)
		{
			std::string filterIDStr = "";
			bool ret = Util::NumToString(reOrder[i].id, &filterIDStr);
			if (!ret)
			{
                LOG(ERROR) << "[Controller::ResetLayerFilters] til::StringToNum failed, reOrder filterID in: " << filterID << ", out: " << filterIDStr;
                continue;
			}

			if (filterIDStr == filterID)
			{
				result.insert(result.begin() + insertPos, reOrder[i]);
				insertPos++;
			}
		}
	}

	return result;
}

UINT64 Controller::CreateFilter(FILTER* filterInfo)
{
	return FilterMgr::GetInstance()->AddFilter(filterInfo);
}

void Controller::DeleteFilter(UINT64 filterID)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		FilterMgr::GetInstance()->DeleteFilter(pFilter);
}

void Controller::ControlFilter(UINT64 filterID, const FILTER& info, FILTER_CONTROL_CMD cmd)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->ControlFilter(info, cmd);
}

void Controller::GetFilterInfo(UINT64 filterID, FILTER* info, FILTER_INFO_CMD cmd)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->GetFilterInfo(info, cmd);
}

void Controller::SetFilterInfo(UINT64 filterID, const FILTER* info)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->SetFilterInfo(info);
}

bool Controller::FindFilterByID(UINT64 filterID)
{
    Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		return true;
	return false;
}

void Controller::SetMixParameter(UINT32 videoModel, VIDEO_MIX_PARAM param)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	
	g_mediaMgr->SetMixParameter(videoModel, param);
}

void Controller::OutputThumbnail(LIVE_MODE mode, UINT64 sceneID, UINT64 canvasID, const std::string& path, IMAGE_FORMAT format, void* cbparam)
{
	Scene* scene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!scene)
		return;

	bool isCur = ModeSceneMgr::GetInstance()->IsCurrentScene(sceneID);
	UINT32 videoModel = ModeSceneMgr::GetInstance()->GetModelByCanvasID(canvasID);
	if (isCur)
	{
		::Sleep(50);
		g_sdkController->PreviewWindowSaveAsImage(videoModel, format, path);
		struct OutputThumbnailEvent evt;
		evt.functor = cbparam;
		evt.success = true;
		eventbus::EventBus::PostEvent(evt);
	}
	else
	{
		Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
		if (pCanvas)
		{
			CANVAS_INFO_EX canvasInfo;
			pCanvas->GetCanvasInfo(&canvasInfo, true);

			canvasInfo.allSync = true;
			pCanvas->SetCanvasInfo(&canvasInfo);

			scene->Select(true, SWITCH_SCENE_NORMAL);
			g_sdkController->SetModelActive(videoModel, true);
			g_sdkController->PreviewWindowEnableDraw(videoModel, true);
			pCanvas->UpdateWholeLayersOrder(canvasInfo.order, NULL, NULL);
			m_thumbnailInfo.mode = mode;
			m_thumbnailInfo.sceneID = sceneID;
			m_thumbnailInfo.videoModel = videoModel;
			m_thumbnailInfo.path = path;
			m_thumbnailInfo.format = format;
			m_thumbnailInfo.cbparam = cbparam;
			m_timer_thumbnail = TimerMgr_SetTimer(0, 3000, this);
		}
	}
}

void Controller::OutputThumbnailInternal(void* param)
{
	Scene* scene = ModeSceneMgr::GetInstance()->GetSceneByID(m_thumbnailInfo.sceneID);
	if (!scene)
		return;

	g_sdkController->PreviewWindowSaveAsImage(m_thumbnailInfo.videoModel, m_thumbnailInfo.format, m_thumbnailInfo.path);
	DeleteScene(m_thumbnailInfo.sceneID);
	struct OutputThumbnailEvent evt;
	evt.functor = m_thumbnailInfo.cbparam;
	evt.success = true;
	eventbus::EventBus::PostEvent(evt);
}

void Controller::HandleTimer(UINT64 id)
{
    if (id == m_timer_thumbnail)
    {
        TimerMgr_KillTimer(m_timer_thumbnail);
        g_cief->GetThreadMgr()->AddTaskToBackThread(new MemberTask<Controller>(this, &Controller::OutputThumbnailInternal, 0));
    }
}

typedef BOOL(WINAPI* LPFN_GLPI)(PSYSTEM_LOGICAL_PROCESSOR_INFORMATION, PDWORD);
INT Controller::GetCpuDeviceInfoExt(CPU_INFO& cpuinfo)
{
    LPFN_GLPI                             glpi;
    BOOL                                  done = FALSE;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION buffer = NULL;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION ptr = NULL;
    DWORD                                 returnLength = 0;
    DWORD                                 logicalProcessorCount = 0;
    DWORD                                 processorCoreCount = 0;
    DWORD                                 processorPackageCount = 0;
    DWORD                                 byteOffset = 0;
    PCACHE_DESCRIPTOR                     Cache;

    glpi = (LPFN_GLPI)GetProcAddress(GetModuleHandle(TEXT("kernel32")), "GetLogicalProcessorInformation");
    if (NULL == glpi)
    {
        LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation is not supported.");
        return (1);
    }

    while (!done)
    {
        DWORD rc = glpi(buffer, &returnLength);

        if (FALSE == rc)
        {
            if (GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                if (buffer)
                    free(buffer);

                buffer = (PSYSTEM_LOGICAL_PROCESSOR_INFORMATION)malloc(
                    returnLength);

                if (NULL == buffer)
                {
                    LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation: Allocation failure");
                    return (2);
                }
            }
            else
            {
                LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation get error %d"), GetLastError();
                return (3);
            }
        }
        else
        {
            done = TRUE;
        }
    }

    ptr = buffer;

    while (byteOffset + sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION) <= returnLength)
    {
        if (ptr->Relationship == RelationProcessorCore)
        {
			++cpuinfo.coreNum;
        }
        byteOffset += sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION);
		++ptr;
    }

    free(buffer);
    buffer = NULL;

    int cpuInfo[4] = {0};
    __cpuid(cpuInfo, 1);

    cpuinfo.family = (cpuInfo[0] >> 8) & 0xf;
    cpuinfo.model = (cpuInfo[0] >> 4) & 0xf;
    cpuinfo.stepping = cpuInfo[0] & 0xf;
    return 0;
}

void Controller::EnumVideoEnc(std::vector<VIDEO_ENCODER_INFO>* oVideoEncoders)
{
	g_sdkController->EnumVideoEncoders(oVideoEncoders);
}

void Controller::GetVideoCodecParam(const std::string& codecID, INT32* oErrCode, VIDEO_CODEC_PARAM* oParam)
{
	g_sdkController->EncoderGetInfo(codecID, oErrCode, oParam);
}

void Controller::StartLive(std::vector<OUTPUT_INFO> infos)
{
	for (auto& info : infos)
	{
		info.videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(info.videoModel);
		m_streamIDMap[info.streamID] = info;
	}
	g_sdkController->StartStream(infos);
}

void Controller::StopLive(const std::vector<STOP_STREAM_PARAM>& streamParams)
{
	g_sdkController->StopStream(streamParams);
	for (const auto& param : streamParams)
	{
		m_streamIDMap.erase(param.streamID);
	}
}

void Controller::SetStreamRoomID(const std::string& roomID)
{
	g_sdkController->StreamSetRoomID(roomID);
}

void Controller::SetMultipleIFrameSEI(std::vector<SEI_INFO> infos)
{
	std::vector<SEI_INFO> seiInfos{};
    for (auto& info : infos)
    {
        info.videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(info.videoModel);
		std::string streamID = "";
		{
            for (const auto& iter : m_streamIDMap)
            {
                const auto& outputInfo = iter.second;
                if (outputInfo.videoModel == info.videoModel)
                {
                    if (outputInfo.streamInfo.type != STREAM_TYPE::STREAM_RECORD_FFMPEG && outputInfo.streamInfo.type != STREAM_TYPE::STREAM_RECORD_FLV)
                    {
                        streamID = outputInfo.streamID;
                        break;
                    }
                }
            }
		}
        if (streamID.empty())
            continue;

		info.streamID = streamID;
		seiInfos.push_back(info);
    }

	bool success = g_sdkController->SetIFrameSEI(seiInfos);
	if (!success)
		LOG(ERROR) << "[Controller::SetMultipleIFrameSEI] SetIFrameSEI failed";
}

void Controller::StartBwProbeStream(OUTPUT_INFO info, bool* running)
{
	info.videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(info.videoModel);
	g_sdkController->BwProbeStartStream(info, running);
}

void Controller::StopBwProbeStream(const std::string& streamID, bool* fallback)
{
	g_sdkController->BwProbeStopStream(streamID, fallback);
}

void Controller::IsStreamInProcess(const std::string& streamID, bool* is_streaming)
{
	g_sdkController->IsStreamInProcess(streamID, is_streaming);
}

void Controller::ABRConfig(const std::string& streamID, UINT32 offset)
{
	g_sdkController->StreamSetAbrConfig(streamID, offset);
}

void Controller::GetActiveStatistic(const std::string& streamID, ACTIVE_STATISTIC* statistic)
{
	g_sdkController->GetStreamActiveStatistic(streamID, statistic);
}

bool Controller::StartRTC(RTC_LINK link)
{
	link.videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(link.videoModel);
	return g_sdkController->RTCControllerStart(link);
}

bool Controller::StopRTC()
{
	return g_sdkController->RTCControllerStop();
}

bool Controller::StartScreenShare(UINT32 videoModel, const CLIP_AREA_INFO& info, bool enableAudio, UINT32 audioTrack)
{
	videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	return g_sdkController->RTCControllerStartScreenShare(videoModel, info, enableAudio, audioTrack);
}

bool Controller::StopScreenShare(UINT32 videoModel)
{
	videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(videoModel);
	return g_sdkController->RTCControllerStopScreenShare(videoModel);
}

bool Controller::UpdateScreenShareClipInfo(const CLIP_AREA_INFO& info)
{
	return g_sdkController->RTCControllerUpdateScreenShareClipInfo(info);
}

bool Controller::UpdateClipScaleInfo(const CLIP_AREA_INFO& info)
{
    return g_sdkController->RTCControllerUpdateClipScaleInfo(info);
}

bool Controller::StartLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerStartLiveTranscoding(args);
}

bool Controller::UpdateLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerUpdateLiveTranscoding(args);
}

bool Controller::StopLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerStopLiveTranscoding(args);
}

bool Controller::StartForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result)
{
	return g_sdkController->RTCControllerStartForwardStreamToRooms(infos, result);
}

bool Controller::UpdateForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result)
{
	return g_sdkController->RTCControllerUpdateForwardStreamToRooms(infos, result);
}

bool Controller::StopForwardStreamToRooms()
{
	return g_sdkController->RTCControllerStopForwardStreamToRooms();
}

bool Controller::PublishVideoStream(MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerPublishVideoStream(type);
}

bool Controller::UnPublishVideoStream(MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerUnPublishVideoStream(type);
}

bool Controller::SendRTCUserMessage(const std::string& uid, const std::string& msg)
{
	return g_sdkController->RTCControllerSendRTCUserMessage(uid, msg);
}

bool Controller::SendRTCRoomMessage(const std::string& msg)
{
	return g_sdkController->RTCControllerSendRTCRoomMessage(msg);
}

bool Controller::SubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerSubscribeScreen(uid, type);
}

bool Controller::UnSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerUnSubscribeScreen(uid, type);
}

bool Controller::SetAudioOutput(const std::string& deviceID)
{
	return g_sdkController->RTCControllerSetAudioOutput(deviceID);
}

bool Controller::SetAudioPropertiesReport(UINT32 interval)
{
	return g_sdkController->RTCControllerSetAudioPropertiesReport(interval);
}

bool Controller::MuteRemoteAudio(const std::string& userID, UINT32 streamIdx, bool muteRemoteAudio)
{
	return g_sdkController->RTCControllerMuteRemoteAudio(userID, streamIdx, muteRemoteAudio);
}

bool Controller::EnableLocalAudio(bool enable)
{
	return g_sdkController->RTCControllerEnableLocalAudio(enable);
}

bool Controller::EnableLocalVideo(bool enable)
{
	return g_sdkController->RTCControllerEnableLocalVideo(enable);
}

void Controller::CameraSourceIsUsingRealCamera(UINT64 layerID, bool* value)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
		pLayer->GetLayerInfo(&layerInfo);

		SOURCE_INFO sourceInfo{};
		GetSourceInfo(layerInfo.sourceID, &sourceInfo);
		if (sourceInfo.type == VISUAL_CAMERA && sourceInfo.isCreated)
			*value = true;
	}

	*value = false;
}

void Controller::GetCPUUsage(float* processUsage, float* sysUsage1, float* sysUsage2)
{
	MonitorMgr::GetInstance()->GetCPUUsage(processUsage, sysUsage1, sysUsage2);
}

void Controller::GetMemUsage(float* processUsage, float* sysUsage, UINT64* totalSize, float* pageFault, float* commitMemoryUsage)
{
	MonitorMgr::GetInstance()->GetMemUsage(processUsage, sysUsage, totalSize, pageFault, commitMemoryUsage);
}

void Controller::GetActiveFPS(float* fps)
{
	g_sdkController->GetActiveFPS(fps);
}

void Controller::GetNotReadyFPS(float* fps)
{
	g_sdkController->GetPresentNotReadyFPS(fps);
}

std::vector<VIDEO_ADAPTER_INFO> Controller::GetGPUInfo()
{
	return MonitorMgr::GetInstance()->GetGPUInfo();
}

CPU_INFO Controller::GetCpuInfo()
{
	static CPU_INFO s_cpuInfo;
	if (!s_cpuInfo.processorName.empty())
	{
		return s_cpuInfo;
	}

	CPU_INFO cpuInfo;
	HKEY hKey = { 0 };
	long lRet = RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", 0, KEY_READ, &hKey);
	if (lRet == ERROR_SUCCESS)
	{
		DWORD dwType = 0;
		WCHAR chCPUName[1024] = { 0 };
		DWORD dwSize = 500;

		lRet = RegQueryValueEx(hKey, L"ProcessorNameString", NULL, &dwType, (LPBYTE)&chCPUName[0], &dwSize);
		if (ERROR_SUCCESS == lRet)
		{
			cpuInfo.processorName = chCPUName;
		}
		else
		{
			LOG_LINE(ERROR) << StringPrintf("RegQueryValueEx ProcessorNameString FAILED: %d", lRet);
		}

		dwType = REG_DWORD;
		DWORD dwValue;
		lRet = RegQueryValueEx(hKey, L"~MHz", NULL, &dwType, (LPBYTE)&dwValue, &dwSize);
		if (ERROR_SUCCESS == lRet)
		{
			cpuInfo.maxClockSpeed = dwValue;
		}
		else
		{
			LOG_LINE(ERROR) << StringPrintf("RegQueryValueEx MHz FAILED: %d", lRet);
		}
	}
	else
	{
		LOG_LINE(ERROR) << StringPrintf("RegOpenKeyEx FAILED: %d", lRet);
	}
	(void)RegCloseKey(hKey);

	SYSTEM_INFO si;
	memset(&si, 0, sizeof(SYSTEM_INFO));
	GetSystemInfo(&si);
	cpuInfo.processNum = si.dwNumberOfProcessors;
	GetCpuDeviceInfoExt(cpuInfo);
	s_cpuInfo = cpuInfo;
	return cpuInfo;
}

std::vector<MONITOR_INFO> Controller::GetMonitorsInfo()
{
	std::vector<MONITOR_INFO> monitors;
	IDXGIFactory* pdxFactory;
	HRESULT hr = CreateDXGIFactory(IID_PPV_ARGS(&pdxFactory));
	if (SUCCEEDED(hr))
	{
		int nAdapter = 0;
		while (1)
		{
			IDXGIAdapter* pdxAdapter;
			hr = pdxFactory->EnumAdapters(nAdapter, &pdxAdapter);
			if (FAILED(hr))
				break;

			DXGI_ADAPTER_DESC dxAdapterDesc;
			hr = pdxAdapter->GetDesc(&dxAdapterDesc);
			if (FAILED(hr))
			{
				LOG_LINE(ERROR) << "GetDesc GPU FAILED";
			}
			int nOutput = 0;
			while (1)
			{
				IDXGIOutput* pdxOutput;
				if (FAILED(pdxAdapter->EnumOutputs(nOutput, &pdxOutput)))
					break;
				MONITOR_INFO monitor;
				monitor.gpuIndex = nOutput;
				DXGI_OUTPUT_DESC dxOutputDesc;
				hr = pdxOutput->GetDesc(&dxOutputDesc);
				DEVMODEW mode;
				if (EnumDisplaySettingsW(dxOutputDesc.DeviceName, ENUM_CURRENT_SETTINGS, &mode))
				{
					monitor.refresh = mode.dmDisplayFrequency;
				}

				int modeIndex = 0;
				int maxWidth = 0, maxHeight = 0;

				// 获取物理最大分辨率
				while (EnumDisplaySettings(dxOutputDesc.DeviceName, modeIndex, &mode))
				{
					if (mode.dmPelsWidth > maxWidth)
					{
						maxWidth = mode.dmPelsWidth;
						maxHeight = mode.dmPelsHeight;
					}
					modeIndex++;
				}

				MONITORINFO mi = { sizeof(mi) };
				GetMonitorInfo(dxOutputDesc.Monitor, &mi);
				monitor.monitor = dxOutputDesc.Monitor;
				monitor.left = mi.rcMonitor.left;
				monitor.right = mi.rcMonitor.right;
				monitor.top = mi.rcMonitor.top;
				monitor.bottom = mi.rcMonitor.bottom;
				monitor.physWidth = maxWidth;
				monitor.physHeight = maxHeight;

				TinyComPtr<IDXGIOutput6> output6;
				if (SUCCEEDED(pdxOutput->QueryInterface(&output6)))
				{
					DXGI_OUTPUT_DESC1 desc1;
					if (SUCCEEDED(output6->GetDesc1(&desc1)) && (desc1.Monitor == dxOutputDesc.Monitor))
					{
						monitor.isHDR = desc1.ColorSpace == DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020 ? TRUE : FALSE;
					}
				}

				monitors.push_back(monitor);
				pdxOutput->Release();
				nOutput++;
			}
			pdxAdapter->Release();
			nAdapter++;
		}
		pdxFactory->Release();
	}
	return monitors;
}

HMONITOR Controller::GetCurrentMonitor()
{
	return ::MonitorFromWindow(m_bottomWnd, MONITOR_DEFAULTTONULL);
}

UINT Controller::GetHAGS()
{
	HKEY  hKey;
	DWORD HwSchModeValue = 0;

	LONG lRes = RegOpenKeyExW(HKEY_LOCAL_MACHINE, LR"(SYSTEM\CurrentControlSet\Control\GraphicsDrivers)", 0, KEY_READ, &hKey);
	if (lRes == ERROR_SUCCESS)
	{
		DWORD dwBufferSize(sizeof(DWORD));
		DWORD nResult(0);
		LONG  nError = ::RegQueryValueExW(hKey,
			L"HwSchMode",
			0,
			NULL,
			reinterpret_cast<LPBYTE>(&nResult),
			&dwBufferSize);
		if (ERROR_SUCCESS == nError)
		{
			HwSchModeValue = nResult;
		}
	}
	return HwSchModeValue;
}

void Controller::StartRenderProfiler()
{
	g_sdkController->ReportTeaDataRenderProfiler();
}

void Controller::StartCollectPerformanceMatrics(const std::vector<PERFORMANCE_MATRICS>& params)
{
	g_sdkController->ReportTeaDataPerformanceMatrics(params);
}

void Controller::SetTTNtpMS(UINT64 ntpMs, UINT64 localMs)
{
	g_sdkController->SetTTNtpMS(ntpMs, localMs);
}

void Controller::GetAudioPerformance(const std::string& audioID, AUDIO_PERFORMANCE_INFO* info)
{
	g_sdkController->AudioSourceGetPerformance(audioID, info);
}

void Controller::OnKey(WPARAM wParam, LPARAM lParam)
{
	g_sdkController->OnKeyboardEvent(wParam, lParam);
}

void* Controller::GetMediaMgrImpl()
{
    return g_mediaMgr;
}

void Controller::StartAiIpc()
{
	AiSdkIPCMgr::GetInstance()->StartLoop();
}

void Controller::StopAiIpc()
{
	AiSdkIPCMgr::GetInstance()->StopLoop();
}

void Controller::SetupTimer(void* param)
{
	ITimerMgr* timeMgr = (ITimerMgr*)g_cief->QueryInterface(LSINAME_TIMERMGR);
	m_timer = timeMgr->SetTimer(0, 5000, (ITimerHandler*)this);
}

void Controller::CloseTimer()
{
    if (m_timer)
    {
        ITimerMgr* timeMgr = (ITimerMgr*)g_cief->QueryInterface(LSINAME_TIMERMGR);
        timeMgr->KillTimer(m_timer);
        m_timer = 0;
    }
}