#include "Canvas.h"
#include "MediaMgr.h"
#include "ModeSceneMgr.h"
#include "AudioMgr.h"
#include "FilterMgr.h"
#include "SourceMgr.h"
#include "stringutil.h"
#include "MediaSDKControllerV2Impl.h"

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;
extern media_mgr::MediaMgr* g_mediaMgr;

Canvas::Canvas() {}

Canvas::~Canvas() {}

void Canvas::SetCanvasInfo(const CANVAS_INFO* info)
{
	m_canvasInfo = *info;
}

void Canvas::UpdateCanvasLayout(CANVAS_INFO* info)
{
	Gdiplus::SizeF oldLayoutSize = { m_canvasInfo.layoutRect.Width, m_canvasInfo.layoutRect.Height };
	SetCanvasInfo(info);
	m_videoModel = ModeSceneMgr::GetInstance()->GetModelByCanvasID(m_canvasInfo.id);

	if (oldLayoutSize.Width > EPS && oldLayoutSize.Height > EPS && m_canvasInfo.layoutRect.Width > EPS && m_canvasInfo.layoutRect.Height > EPS && (std::abs(oldLayoutSize.Width - m_canvasInfo.layoutRect.Width) > EPS || std::abs(oldLayoutSize.Height - m_canvasInfo.layoutRect.Height) > EPS))
    {
        for (const auto& layer : m_layers)
        {
            if (!layer)
                continue;

            Gdiplus::SizeF newLayoutSize{m_canvasInfo.layoutRect.Width, m_canvasInfo.layoutRect.Height};
            layer->SetLayerSize(&oldLayoutSize, &newLayoutSize);

            LAYER_INFO layerInfo{};
            layer->GetLayerInfo(&layerInfo);
            if (layerInfo.transform.size.Width > EPS && layerInfo.transform.size.Height > EPS && layerInfo.layout != LAYOUT_NONE)
            {
                Gdiplus::RectF newLayoutRect{0, 0, m_canvasInfo.layoutRect.Width, m_canvasInfo.layoutRect.Height};
                if (layerInfo.refLayout.z > EPS && layerInfo.refLayout.w > EPS)
                {
                    newLayoutRect.X = layerInfo.refLayout.x * m_canvasInfo.layoutRect.Width;
                    newLayoutRect.Y = layerInfo.refLayout.y * m_canvasInfo.layoutRect.Height;
                    newLayoutRect.Width = layerInfo.refLayout.z * m_canvasInfo.layoutRect.Width;
                    newLayoutRect.Height = layerInfo.refLayout.w * m_canvasInfo.layoutRect.Height;
                }
                layer->SetLayerLayout(&newLayoutRect);
            }

            g_mediaMgr->ControlLayer(layerInfo, (LAYER_CONTROL_CMD)(LAYER_CONTROL_SET_TRANSLATE | LAYER_CONTROL_SET_SCALE | LAYER_CONTROL_SET_MIN_SCALE | LAYER_CONTROL_SET_MAX_SCALE));
        }
    }

    if (m_syncMedia)
    {
        g_mediaMgr->SetModelLayout(m_videoModel, info->rect, info->layoutRect, info->outputSize);
    }
}

void Canvas::SetParent(UINT64 id)
{
	m_canvasInfo.sceneID = id;
}

UINT64 Canvas::AddLayerWithInfo(LAYER_INFO* layerInfo, bool calcLayout, const UINT64* id)
{
	UINT64 layerID = ModeSceneMgr::GetInstance()->CreateLayer(layerInfo, id);
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	AddLayer(pLayer, calcLayout);	
	return layerID;
}

bool Canvas::AddLayer(Layer* pLayer, bool calcLayout)
{
	if (!pLayer)
		return false;
	LAYER_INFO layerInfo{};
	pLayer->GetLayerInfo(&layerInfo);

	auto it = std::find(m_layers.begin(), m_layers.end(), pLayer);
	if (it == m_layers.end())
	{
		m_layers.push_back(pLayer);
		pLayer->SetParent(m_canvasInfo.id);
	}
	else
	{
		LOG(ERROR) << "[Canvas::AddLayer] it != m_layers.end()";
		return false;
	}

	auto st = std::find(m_order.begin(), m_order.end(), layerInfo.id);
	if (st != m_order.end())
	{
		m_order.erase(st);
	}
	m_order.insert(m_order.begin(), layerInfo.id);

	Gdiplus::RectF canvasLayoutRect = { m_canvasInfo.layoutRect.X, m_canvasInfo.layoutRect.Y, m_canvasInfo.layoutRect.Width, m_canvasInfo.layoutRect.Height };
	if (canvasLayoutRect.Width > EPS && canvasLayoutRect.Height > EPS && calcLayout)
	{
		Gdiplus::SizeF tarSize = { layerInfo.targetSize.Width, layerInfo.targetSize.Height };
		if (tarSize.Width > EPS && tarSize.Height > EPS &&
			(std::abs(m_canvasInfo.layoutRect.Width - tarSize.Width) > EPS || std::abs(m_canvasInfo.layoutRect.Height - tarSize.Height) > EPS))
		{
			Gdiplus::SizeF canvasLayoutSize = { canvasLayoutRect.Width, canvasLayoutRect.Height };
			pLayer->SetLayerSize(&tarSize, &canvasLayoutSize);
		}

		if (layerInfo.transform.size.Width > EPS && layerInfo.transform.size.Height > EPS && layerInfo.layout != LAYOUT_NONE)
		{
			if (layerInfo.refLayout.z > EPS && layerInfo.refLayout.w > EPS)
			{
				canvasLayoutRect.X = layerInfo.refLayout.x * m_canvasInfo.layoutRect.Width;
				canvasLayoutRect.Y = layerInfo.refLayout.y * m_canvasInfo.layoutRect.Height;
				canvasLayoutRect.Width = layerInfo.refLayout.z * m_canvasInfo.layoutRect.Width;
				canvasLayoutRect.Height = layerInfo.refLayout.w * m_canvasInfo.layoutRect.Height;
			}
			pLayer->SetLayerLayout(&canvasLayoutRect);
		}
	}

	//Sync
	if (m_syncMedia)
	{
		return pLayer->SyncToMediaCanvas(m_canvasInfo.id, true);
	}
	else
	{
		pLayer->GetLayerInfo(&layerInfo);
		layerInfo.createNotCurrentCanvas = true;
		pLayer->SetLayerInfo(&layerInfo);
	}

	return true;
}

void Canvas::RemoveLayer(Layer* layer)
{
	if (!layer)
	{
		LOG(WARNING) << "[Canvas::RemoveLayer] layer is illegle";
		return;
	}

	auto it = std::find(m_layers.begin(), m_layers.end(), layer);
	if (it != m_layers.end())
	{
		m_layers.erase(it);
		LAYER_INFO layerInfo = {};
		layer->GetLayerInfo(&layerInfo);

		auto st = std::find(m_order.begin(), m_order.end(), layerInfo.id);
		if (st != m_order.end())
		{
			m_order.erase(st);
		}

		layer->SyncToMediaCanvas(m_canvasInfo.id, false);
	}
}

void Canvas::GetCanvasInfo(CANVAS_INFO_EX* info, bool child /*= false*/)
{
    *(CANVAS_INFO*)info = m_canvasInfo;
    if (child)
    {
        for (int i = 0; i < m_layers.size(); i++)
        {
            LAYER_INFO layerInfo = {};
            m_layers[i]->GetLayerInfo(&layerInfo);
            info->layers.push_back(layerInfo);
        }
        info->order = m_order;
    }
}

void Canvas::Select(bool in)
{
	LOG(INFO) << "[Canvas::Select] in: " << in;
	std::string canvas_id = "";
	Util::NumToString(m_canvasInfo.id, &canvas_id);

	std::string filterIDStr = "";
    Util::NumToString(m_canvasInfo.filter.id, &filterIDStr);

	bool enableTransition = !filterIDStr.empty();

	if (in)
	{
		if (m_videoModel == UINT32_MAX)
		{
			LOG(ERROR) << "[Canvas::Select] videoModel is illegle, videoModel: " << m_videoModel;
			return;
		}

		m_syncMedia = true;
		g_mediaMgr->SetModelLayout(m_videoModel, m_canvasInfo.rect, m_canvasInfo.layoutRect, m_canvasInfo.outputSize, m_canvasInfo.hwnd);
        if (!m_canvasInfo.isCreated && g_sdkController->CreateCanvas(canvas_id, m_videoModel))
        {
            if (m_canvasInfo.allSync)
            {
                for (int i = 0; i < m_layers.size(); ++i)
                {
                    m_layers[i]->SyncToMediaCanvas(m_canvasInfo.id, true);
                }
            }
            m_canvasInfo.isCreated = true;
        }

        for (int i = 0; i < m_layers.size(); ++i)
        {
            LAYER_INFO layerInfo{};
            m_layers[i]->GetLayerInfo(&layerInfo);

			SOURCE_INFO sourceInfo{};
            SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

            VISUAL_TYPE type = SourceMgr::GetInstance()->GetCompositeRealType(sourceInfo.id);

			if ((enableTransition && (type == VISUAL_CAMERA || type == VISUAL_ANALOG)))
                SyncLayerSource(layerInfo, &sourceInfo);

            if ((!m_canvasInfo.allSync && type != VISUAL_BROWSER) || layerInfo.createNotCurrentCanvas)
				SyncLayerSource(layerInfo, &sourceInfo);
        }
        m_canvasInfo.allSync = false;
        
        g_sdkController->SetCurrentCanvas(m_videoModel, canvas_id, filterIDStr);
	}
	else
	{
		for (int i = 0; i < m_layers.size(); ++i)
		{
			LAYER_INFO layerInfo{};
			m_layers[i]->GetLayerInfo(&layerInfo);

			SOURCE_INFO sourceInfo{};
			SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

			VISUAL_TYPE type = SourceMgr::GetInstance()->GetCompositeRealType(sourceInfo.id);
            /* 
            if (layerInfo.deactiveWhenNotShow && (type == VISUAL_CAMERA || type == VISUAL_ANALOG || type == VISUAL_BROWSER))
            {
            */
				Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceInfo.id);
				if (sourceInfo.isCreated && pSource && pSource->DestroySource())
				{
					sourceInfo.isCreated = false;
					pSource->SetSourceInfo(&sourceInfo);
				}

				if (!m_layers[i]->SyncToMediaCanvas(m_canvasInfo.id, false))
                {
                    LOG(ERROR) << "[Canvas::Select] SyncToMediaCanvas failed, in: " << in << ", canvasID: " << m_canvasInfo.id;
                    continue;
                }
            /*
            }
            */
		}
		m_syncMedia = false;
	}
}

void Canvas::SyncLayerSource(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo)
{
    if (m_syncMedia && sourceInfo)
    {
        if (!sourceInfo->isCreated && SourceMgr::GetInstance()->CreateSource(sourceInfo, &sourceInfo->id) > 0)
			LOG(INFO) << "[Canvas::SyncLayerSource] CreateSource success, sourceID: " << sourceInfo->id << ", layerID: " << layerInfo.id;

		Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerInfo.id);
        if (pLayer && sourceInfo->isCreated)
        {
			if (layerInfo.createNotCurrentCanvas)
				layerInfo.createNotCurrentCanvas = false;

            layerInfo.sourceID = sourceInfo->id;
            pLayer->SetLayerInfo(&layerInfo);
            if (!pLayer->SyncToMediaCanvas(layerInfo.canvasID, true))
				LOG(ERROR) << "[Canvas::SyncLayerSource] SyncToMediaCanvas failed, in: " << true << ", canvasID: " << layerInfo.canvasID << ", layerID: " << layerInfo.id << ", sourceID: " << layerInfo.sourceID;
        }
    }
}

void Canvas::PreLoadCanvas(bool in)
{
	LOG(INFO) << "[Canvas::PreLoadCanvas] in: " << in;
	if (in)
	{
        if (m_videoModel == UINT32_MAX)
        {
            LOG(ERROR) << "[Canvas::PreLoadCanvas] videoModel is illegle, videoModel: " << m_videoModel;
            return;
        }
        m_syncMedia = true;

        std::string canvas_id = "";
        Util::NumToString(m_canvasInfo.id, &canvas_id);
        g_mediaMgr->SetModelLayout(m_videoModel, m_canvasInfo.rect, m_canvasInfo.layoutRect, m_canvasInfo.outputSize, m_canvasInfo.hwnd);
        if (!m_canvasInfo.isCreated && g_sdkController->CreateCanvas(canvas_id, m_videoModel))
			m_canvasInfo.isCreated = true;
	}
	else
	{
        for (int i = 0; i < m_layers.size(); ++i)
        {
			LAYER_INFO layerInfo{};
			m_layers[i]->GetLayerInfo(&layerInfo);
			
			SOURCE_INFO sourceInfo{};
			SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

			const auto& iter = std::find(sourceInfo.layerIDs.begin(), sourceInfo.layerIDs.end(), layerInfo.id);
            if (iter != sourceInfo.layerIDs.end())
            {
                sourceInfo.layerIDs.erase(iter);
                if (sourceInfo.layerIDs.size() == 0)
                {
                    LOG(INFO) << "[Canvas::PreLoadCanvas] source layerIDs is empty, destroy this source, sourceID: " << sourceInfo.id << ", sourceType: " << sourceInfo.type;
                    SourceMgr::GetInstance()->DestroySource(sourceInfo.id);
                }
                else
                {
                    SourceMgr::GetInstance()->SetSourceInfoByID(sourceInfo.id, sourceInfo);
                }
            }

			if (!m_layers[i]->SyncToMediaCanvas(m_canvasInfo.id, false))
			{
                LOG(ERROR) << "[Canvas::LoadCanvas] SyncToMediaCanvas failed, in: " << in << ", canvasID: " << m_canvasInfo.id;
                continue;
			}
        }
		m_syncMedia = false;
	}
}

void Canvas::ReLoadCanvas(bool in)
{
	LOG(INFO) << "[Canvas::ReLoadCanvas] in: " << in;
	RePreLoadCanvas(in, true);

	if (in)
	{
		std::string canvas_id = "";
		Util::NumToString(m_canvasInfo.id, &canvas_id);
		std::string filterIDStr = "";
		Util::NumToString(m_canvasInfo.filter.id, &filterIDStr);
		g_sdkController->SetCurrentCanvas(m_videoModel, canvas_id, filterIDStr);
	}
}

void Canvas::RePreLoadCanvas(bool in, bool isReload /* = false */)
{
	LOG(INFO) << "[Canvas::RePreLoadCanvas] in: " << in;
	if (in)
	{
		if (m_videoModel == UINT32_MAX)
		{
			LOG(ERROR) << "[Canvas::RePreLoadCanvas] videoModel is illegle, videoModel: " << m_videoModel;
			return;
		}
		m_syncMedia = true;

		std::string canvas_id = "";
		Util::NumToString(m_canvasInfo.id, &canvas_id);
		g_mediaMgr->SetModelLayout(m_videoModel, m_canvasInfo.rect, m_canvasInfo.layoutRect, m_canvasInfo.outputSize, m_canvasInfo.hwnd);
		if (!g_sdkController->CreateCanvas(canvas_id, m_videoModel))
		{
			LOG(ERROR) << "[Canvas::RePreLoadCanvas] CreateCanvas failed, canvasID: " << m_canvasInfo.id << ", videoModel: " << m_videoModel;
			return;
		}
		m_canvasInfo.isCreated = true;

		for (int i = 0; i < m_layers.size(); ++i)
		{
			LAYER_INFO layerInfo{};
			m_layers[i]->GetLayerInfo(&layerInfo);

			SOURCE_INFO sourceInfo{};
            SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

			if (!sourceInfo.isCreated)
			{
				bool notCreate = sourceInfo.type == VISUAL_BROWSER || (!isReload && layerInfo.deactiveWhenNotShow && (sourceInfo.type == VISUAL_CAMERA || sourceInfo.type == VISUAL_ANALOG));
                if (!notCreate && SourceMgr::GetInstance()->CreateSource(&sourceInfo, &sourceInfo.id) > 0)
                {
                    layerInfo.sourceID = sourceInfo.id;
                    m_layers[i]->SetLayerInfo(&layerInfo);
                    LOG(INFO) << "[Canvas::RePreLoadCanvas] CreateSource success, sourceID: " << sourceInfo.id << ", sourceType: " << sourceInfo.type;
                }
                else
                {
                    LOG(ERROR) << "[Canvas::RePreLoadCanvas] CreateSource failed or it's browser, sourceID: " << sourceInfo.id << ", sourceType: " << sourceInfo.type;
                    continue;
                }
			}

			if (!layerInfo.isCreated && sourceInfo.isCreated)
			{
                if (m_layers[i]->SyncToMediaCanvas(m_canvasInfo.id, true))
                {
                    LOG(INFO) << "[Canvas::RePreLoadCanvas] SyncToMediaCanvas success, layerID: " << layerInfo.id;
                }
                else
                {
                    LOG(ERROR) << "[Canvas::RePreLoadCanvas] SyncToMediaCanvas failed, layerID: " << layerInfo.id;
                    continue;
                }
			}
		}

		AddFilter(m_canvasInfo.id, &m_canvasInfo.filter);
	}
	else
	{
		m_canvasInfo.isCreated = false;
		for (int i = 0; i < m_layers.size(); ++i)
		{
			LAYER_INFO layerInfo{};
			m_layers[i]->GetLayerInfo(&layerInfo);

			if (Source* pSource = SourceMgr::GetInstance()->GetSourceByID(layerInfo.sourceID))
			{
				SOURCE_INFO sourceInfo{};
				pSource->GetSourceInfo(&sourceInfo);

				sourceInfo.isCreated = false;
				pSource->SetSourceInfo(&sourceInfo);
			}

			layerInfo.isCreated = false;
			m_layers[i]->SetLayerInfo(&layerInfo);
		}
	}
}

void Canvas::GetLayersOrder(std::vector<UINT64>* order)
{
	*order = m_order;
}

void Canvas::UpdateWholeLayersOrder(std::vector<UINT64> layerIDs, std::vector<UINT64>* moreIDs, std::vector<UINT64>* lessIDs)
{
    std::set<UINT64> layerSet(layerIDs.begin(), layerIDs.end());
    std::set<UINT64> orderSet(m_order.begin(), m_order.end());

	// 1. return more layerIDs
	if (moreIDs)
	{
		std::set_difference(layerSet.begin(), layerSet.end(),
			                orderSet.begin(), orderSet.end(),
			                std::back_inserter(*moreIDs));
	}

	// return unuse layerIDs
	if (lessIDs)
	{
		std::set_difference(orderSet.begin(), orderSet.end(),
			                layerSet.begin(), layerSet.end(),
			                std::back_inserter(*lessIDs));
	}

	// store layerIDs new order
	m_order = layerIDs;

	// sync to media
	if (m_syncMedia)
	{
		std::vector<std::string> layerIDStr{};
		for (int i = 0; i < m_order.size(); i++)
		{
			std::string idstr;
			Util::NumToString(m_order[i], &idstr);
			layerIDStr.push_back(idstr);
		}
		g_sdkController->LayerSetOrder(m_videoModel, layerIDStr);
	}
}

void Canvas::MoveLayerOrder(UINT64 layerID, MOVE_ORDER move)
{
	bool flag = false;
	if (move == MOVE_UP)
	{
		for (int i = 0; i < m_order.size(); i++)
		{
			if (m_order[i] == layerID && i != 0)
			{
				m_order[i] = m_order[i - 1];
				m_order[i - 1] = layerID;
				flag = true;
				break;
			}
		}
	}
	else if (move == MOVE_DOWN)
	{
		for (int i = 0; i < m_order.size(); i++)
		{
			if (m_order[i] == layerID && i != m_order.size() - 1)
			{
				m_order[i] = m_order[i + 1];
				m_order[i + 1] = layerID;
				flag = true;
				break;
			}
		}
	}
	else if (move == MOVE_TOP)
	{
		for (int i = 0; i < m_order.size(); i++)
		{
			if (m_order[i] == layerID && i != 0)
			{
				memmove(&m_order[1], &m_order[0], i * sizeof(UINT64));
				m_order[0] = layerID;
				flag = true;
				break;
			}
		}
	}
	else if (move == MOVE_BOTTOM)
	{
		for (int i = 0; i < m_order.size(); i++)
		{
			if (m_order[i] == layerID && i != m_order.size() - 1)
			{
				memmove(&m_order[i], &m_order[i + 1], (m_order.size() - 1 - i) * sizeof(UINT64));
				m_order[m_order.size() - 1] = layerID;
				flag = true;
				break;
			}
		}
	}

	if (m_syncMedia && flag)
	{
		std::vector<std::string> layerIDStr{};
		for (int i = 0; i < m_order.size(); i++)
		{
			std::string idstr;
			Util::NumToString(m_order[i], &idstr);
			layerIDStr.push_back(idstr);
		}
		g_sdkController->LayerSetOrder(m_videoModel, layerIDStr);
	}
}

void Canvas::AddFilter(UINT64 id, FILTER* info)
{
    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), id);
    if (st == info->mediaIDs.end())
    {
        info->mediaIDs.push_back(id);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(WARNING) << "[Canvas::AddFilter] same filter add to same canvas";
        return;
    }

	if (m_canvasInfo.filter.id != info->id)
	{
		m_canvasInfo.filter = *info; 
	}
    else
    {
        LOG(WARNING) << "[Canvas::AddFilter] same canvas added same filter";
        return;
    }

	if (!info->isCreated && !g_mediaMgr->AddFilter(id, *info))
    {
        LOG(ERROR) << "[Canvas::AddFilter] AddFilter failed, filterID: " << id << " filterType: " << info->type;
        info->isCreated = false;
        return;
    }

    info->isCreated = true;
    FilterMgr::GetInstance()->SetFilterInfo(info);
}

void Canvas::RemoveFilter(UINT64 id, FILTER* info)
{
	if (m_canvasInfo.filter.id == info->id)
	{
		m_canvasInfo.filter = {};
	}
    else
    {
        LOG(WARNING) << "[Layer::RemoveFilter] canvas have not filter";
        return;
    }

    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), id);
    if (st != info->mediaIDs.end())
    {
        info->mediaIDs.erase(st);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(WARNING) << "[Layer::RemoveFilter] filter do not belong to any canvas";
        return;
    }

	// Use filter.Remove Real Destroy this Filter
	/*
    if (info->isCreated)
        g_mediaMgr->RemoveFilter(id, *info);
	*/
}
