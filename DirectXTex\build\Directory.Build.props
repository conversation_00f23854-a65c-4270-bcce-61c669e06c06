<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!--
      Copyright (c) Microsoft Corporation.
      Licensed under the MIT License.
  -->

  <PropertyGroup>
    <ExtractedFolder Condition="'$(ExtractedFolder)'==''">C:\xtracted\</ExtractedFolder>
    <ExtractedFolder Condition="!HasTrailingSlash('$(ExtractedFolder)')">$(ExtractedFolder)\</ExtractedFolder>

    <_AlternativeVCTargetsPath170>$(ExtractedFolder)VCTargets170\</_AlternativeVCTargetsPath170>
    <_AlternativeVCTargetsPath160>$(ExtractedFolder)VCTargets160\</_AlternativeVCTargetsPath160>
    <_AlternativeVCTargetsPath150 Condition="'$(GDKEditionNumber)' != '' AND '$(GDKEditionNumber)' &lt; '241000'">$(ExtractedFolder)VCTargets150\</_AlternativeVCTargetsPath150>

    <!-- Workaround for VS bug -->
    <MinimumVisualStudioVersion>15.0</MinimumVisualStudioVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(GDKEditionNumber)' != '' AND '$(GDKEditionNumber)' &lt; '220300'">
    <_PCNuGetPackage>Microsoft.PGDK</_PCNuGetPackage>
    <_XboxNuGetPackage>Microsoft.GDK</_XboxNuGetPackage>
  </PropertyGroup>

  <PropertyGroup Condition="'$(GDKEditionNumber)' != '' AND '$(GDKEditionNumber)' &gt;= '220300'">
    <_PCNuGetPackage>Microsoft.GDK.PC</_PCNuGetPackage>
    <_XboxNuGetPackage>Microsoft.GDK.Xbox</_XboxNuGetPackage>
  </PropertyGroup>

  <PropertyGroup Condition="'$(GDKEditionNumber)' != '' AND '$(GDKEditionNumber)' &lt; '241000'">
    <_PCNuGetPackage>$(_PCNuGetPackage).$(GDKEditionNumber)</_PCNuGetPackage>
    <_XboxNuGetPackage>$(_XboxNuGetPackage).$(GDKEditionNumber)</_XboxNuGetPackage>
  </PropertyGroup>

  <!-- Windows SDK NuGet -->
  <Import Condition="'$(WSDKEnableBWOI)' == 'true'"
          Project="$(ExtractedFolder)Microsoft.Windows.SDK.cpp\build\Microsoft.Windows.SDK.cpp.props" />

  <Import Condition="'$(WSDKEnableBWOI)' == 'true' and $(Platform.Contains('x64'))"
          Project="$(ExtractedFolder)Microsoft.Windows.SDK.cpp.x64\build\native\Microsoft.Windows.SDK.cpp.x64.props" />

  <Import Condition="'$(WSDKEnableBWOI)' == 'true' and '$(Platform)' == 'Win32'"
          Project="$(ExtractedFolder)Microsoft.Windows.SDK.cpp.x86\build\native\Microsoft.Windows.SDK.cpp.x86.props" />

  <Import Condition="'$(WSDKEnableBWOI)' == 'true' and '$(Platform)' == 'ARM64'"
          Project="$(ExtractedFolder)Microsoft.Windows.SDK.cpp.arm64\build\native\Microsoft.Windows.SDK.cpp.arm64.props" />

  <!-- Microsoft GDK NuGet -->
  <Import Condition="'$(GDKEnableBWOI)' == 'true' and $(Platform.Contains('x64')) and Exists('$(ExtractedFolder)$(_PCNuGetPackage)\build\$(_PCNuGetPackage).props')"
          Project="$(ExtractedFolder)$(_PCNuGetPackage)\build\$(_PCNuGetPackage).props" />

  <Import Condition="'$(GDKEnableBWOI)' == 'true' and $(Platform.Contains('x64')) and Exists('$(ExtractedFolder)$(_XboxNuGetPackage)\build\$(_XboxNuGetPackage).props')"
          Project="$(ExtractedFolder)$(_XboxNuGetPackage)\build\$(_XboxNuGetPackage).props" />

  <!-- Remove copy of real gameos.xvd since this is a build validation only pipeline -->
  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Xbox.XboxOne.x64' or '$(Platform)' == 'Gaming.Xbox.Scarlett.x64'">
    <GameOSFilePath>$(MSBuildThisFileDirectory)build\placeholder.xvd</GameOSFilePath>
  </PropertyGroup>

  <!--
  ****************************************************************************************************
  VCTargetsPath redirection (VS 2019)
  ****************************************************************************************************
  -->
  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Xbox.XboxOne.x64' and '$(VisualStudioVersion)' == '16.0'">
    <AdditionalVCTargetsPath>$(_AlternativeVCTargetsPath160)</AdditionalVCTargetsPath>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16>$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Xbox.Scarlett.x64' and '$(VisualStudioVersion)' == '16.0'">
    <AdditionalVCTargetsPath>$(_AlternativeVCTargetsPath160)</AdditionalVCTargetsPath>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16>$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Desktop.x64' and '$(VisualStudioVersion)' == '16.0'">
    <AdditionalVCTargetsPath>$(_AlternativeVCTargetsPath160)</AdditionalVCTargetsPath>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16>$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
  </PropertyGroup>

  <!--
  ****************************************************************************************************
  VCTargetsPath redirection (VS 2022)
  ****************************************************************************************************
  -->
  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Xbox.XboxOne.x64' and '$(VisualStudioVersion)' == '17.0'">
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16 Condition="'$(_AlternativeVCTargetsPath160)'!=''">$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
    <VCTargetsPath17>$(_AlternativeVCTargetsPath170)</VCTargetsPath17>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Xbox.Scarlett.x64' and '$(VisualStudioVersion)' == '17.0'">
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16 Condition="'$(_AlternativeVCTargetsPath160)'!=''">$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
    <VCTargetsPath17>$(_AlternativeVCTargetsPath170)</VCTargetsPath17>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Platform)' == 'Gaming.Desktop.x64' and '$(VisualStudioVersion)' == '17.0'">
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <VCTargetsPath15 Condition="'$(_AlternativeVCTargetsPath150)'!=''">$(_AlternativeVCTargetsPath150)</VCTargetsPath15>
    <VCTargetsPath16 Condition="'$(_AlternativeVCTargetsPath160)'!=''">$(_AlternativeVCTargetsPath160)</VCTargetsPath16>
    <VCTargetsPath17>$(_AlternativeVCTargetsPath170)</VCTargetsPath17>
  </PropertyGroup>
</Project>