# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# http://go.microsoft.com/fwlink/?LinkId=248926

name: "CodeQL"

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
    paths-ignore:
      - '*.md'
      - LICENSE
      - '.nuget/*'
      - build/*.cmd
      - build/*.json
      - build/*.props
      - build/*.ps1
      - build/*.targets
      - build/*.yml
  schedule:
    - cron: '43 3 * * 3'

permissions:
  contents: read

jobs:
  analyze:
    name: Analyze (C/C++)
    runs-on: windows-latest
    timeout-minutes: 360
    permissions:
      actions: read  # for github/codeql-action/init to get workflow details
      contents: read  # for actions/checkout to fetch code
      security-events: write  # for github/codeql-action/autobuild to send a status report
      packages: read

    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - name: 'Install Ninja'
      run: choco install ninja

    - uses: ilammy/msvc-dev-cmd@0b201ec74fa43914dc39ae48a89fd1d8cb592756 # v1.13.0

    - name: Initialize CodeQL
      uses: github/codeql-action/init@b56ba49b26e50535fa1e7f7db0f4f7b4bf65d80d # v3.28.10
      with:
        languages: c-cpp
        build-mode: manual

    - name: 'Configure CMake'
      working-directory: ${{ github.workspace }}
      run: cmake --preset=x64-Debug

    - name: 'Build'
      working-directory: ${{ github.workspace }}
      run: cmake --build out\build\x64-Debug

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@b56ba49b26e50535fa1e7f7db0f4f7b4bf65d80d # v3.28.10
      with:
        category: "/language:c-cpp"
