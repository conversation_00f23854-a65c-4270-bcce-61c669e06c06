<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BC.cpp" />
    <ClCompile Include="BC4BC5.cpp" />
    <ClCompile Include="BC6HBC7.cpp" />
    <ClCompile Include="BCDirectCompute.cpp" />
    <ClCompile Include="DirectXTexCompress.cpp" />
    <ClCompile Include="DirectXTexCompressGPU.cpp" />
    <ClCompile Include="DirectXTexConvert.cpp" />
    <ClCompile Include="DirectXTexD3D11.cpp" />
    <ClCompile Include="DirectXTexD3D12.cpp" />
    <ClCompile Include="DirectXTexDDS.cpp" />
    <ClCompile Include="DirectXTexFlipRotate.cpp" />
    <ClCompile Include="DirectXTexHDR.cpp" />
    <ClCompile Include="DirectXTexImage.cpp" />
    <ClCompile Include="DirectXTexMipmaps.cpp" />
    <ClCompile Include="DirectXTexMisc.cpp" />
    <ClCompile Include="DirectXTexNormalMaps.cpp" />
    <ClCompile Include="DirectXTexPMAlpha.cpp" />
    <ClCompile Include="DirectXTexResize.cpp" />
    <ClCompile Include="DirectXTexTGA.cpp" />
    <ClCompile Include="DirectXTexUtil.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="DirectXTexWIC.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Common\d3dx12.h" />
    <ClInclude Include="BC.h" />
    <ClInclude Include="BCDirectCompute.h" />
    <ClInclude Include="DDS.h" />
    <ClInclude Include="DirectXTex.h" />
    <ClInclude Include="DirectXTexP.h" />
    <ClInclude Include="filters.h" />
    <ClInclude Include="scoped.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DirectXTex.inl" />
    <None Include="Shaders\CompileShaders.cmd" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Shaders\BC6HEncode.hlsl">
      <FileType>Document</FileType>
    </None>
    <None Include="Shaders\BC7Encode.hlsl">
      <FileType>Document</FileType>
    </None>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{fb3f52b5-bfe8-43fd-836f-363735dab738}</ProjectGuid>
    <Keyword>StaticLibrary</Keyword>
    <ProjectName>DirectXTex</ProjectName>
    <RootNamespace>DirectXTex</RootNamespace>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>14.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.18362.0</WindowsTargetPlatformMinVersion>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>Bin\Windows10_2022\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>DirectXTex</TargetName>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles />
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles />
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles>
      </ForcedUsingFiles>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles>
      </ForcedUsingFiles>
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
      <GuardEHContMetadata>true</GuardEHContMetadata>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles />
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <ForcedUsingFiles />
      <CompileAsWinRT>false</CompileAsWinRT>
      <SDLCheck>true</SDLCheck>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>EnableAllWarnings</WarningLevel>
      <PrecompiledHeaderFile>DirectXTexP.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir);..\Common;$(ProjectDir)Shaders\Compiled;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/Zc:twoPhase- /Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
      <OpenMPSupport>true</OpenMPSupport>
      <ExternalWarningLevel>Level4</ExternalWarningLevel>
      <GuardEHContMetadata>true</GuardEHContMetadata>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <Target Name="ATGEnsureShaders" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <_ATGFXCPath>$(WindowsSDK_ExecutablePath_x64.Split(';')[0])</_ATGFXCPath>
      <_ATGFXCPath>$(_ATGFXCPath.Replace("x64",""))</_ATGFXCPath>
      <_ATGFXCPath Condition="'$(_ATGFXCPath)' != '' and !HasTrailingSlash('$(_ATGFXCPath)')">$(_ATGFXCPath)\</_ATGFXCPath>
      <_ATGFXCVer>$([System.Text.RegularExpressions.Regex]::Match($(_ATGFXCPath), `10\.0\.\d+\.0`))</_ATGFXCVer>
      <_ATGFXCVer Condition="'$(_ATGFXCVer)' != '' and !HasTrailingSlash('$(_ATGFXCVer)')">$(_ATGFXCVer)\</_ATGFXCVer>
    </PropertyGroup>
    <Exec Condition="!Exists('Shaders/Compiled/BC6HEncode_EncodeBlockCS.inc')" WorkingDirectory="$(ProjectDir)Shaders" Command="CompileShaders" EnvironmentVariables="WindowsSdkVerBinPath=$(_ATGFXCPath);WindowsSDKVersion=$(_ATGFXCVer);CompileShadersOutput=$(ProjectDir)Shaders/Compiled" LogStandardErrorAsError="true" />
    <PropertyGroup>
      <_ATGFXCPath />
      <_ATGFXCVer />
    </PropertyGroup>
  </Target>
  <Target Name="ATGDeleteShaders" AfterTargets="Clean">
    <ItemGroup>
      <_ATGShaderHeaders Include="$(ProjectDir)Shaders/Compiled/*.inc" />
      <_ATGShaderSymbols Include="$(ProjectDir)Shaders/Compiled/*.pdb" />
    </ItemGroup>
    <Delete Files="@(_ATGShaderHeaders)" />
    <Delete Files="@(_ATGShaderSymbols)" />
  </Target>
</Project>