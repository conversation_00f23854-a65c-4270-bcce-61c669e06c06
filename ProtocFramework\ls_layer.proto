// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_layer;
import "ls_base.proto";
import "ls_basicenum.proto";

enum LAYER_LAYOUT
{
	LAYER_LAYOUT_NONE = 0;
	LAYER_LAYOUT_CONTAIN_RATIO_FILL = 1;
	LAYER_LAYOUT_TILE_FILL = 2;
	LAYER_LAYOUT_CENTERED = 3;
	LAYER_LAYOUT_COVER_RATIO_FILL = 4;
	LAYER_LAYOUT_HORIZONTAL_CENTERED = 5;
	LAYER_LAYOUT_VERTICAL_CENTERED = 6;
};

enum LAYER_FIXED_EDGE
{
	LAYER_FIXED_EDGE_NONE = 0;
	LAYER_FIXED_EDGE_WIDTH = 1;
	LAYER_FIXED_EDGE_HEIGHT = 2;
	LAYER_FIXED_EDGE_LONGEST = 3;
	LAYER_FIXED_EDGE_SHORTEST = 4;
}

enum LAYER_FLAG
{
    LAYER_FLAG_NO_FLAG = 0;
    LAYER_FLAG_SUCCESS_STATE = 1;
    LAYER_FLAG_VISIBLE_STATE = 2;
    LAYER_FLAG_ACTIVE_STATE = 3;
    LAYER_FLAG_LOCK_STATE = 4;
    LAYER_FLAG_SELECT_STATE = 5;
    LAYER_FLAG_HITTEST_STATE = 6;
    LAYER_FLAG_FLAG_SYNC = 7;
    LAYER_FLAG_FLAG_ASYNC = 8;
    LAYER_FLAG_OUTPUT_FILTER = 9;
    LAYER_FLAG_RENDER_FILTER = 10;
    LAYER_FLAG_RTC_SCREEN_FILTER = 11;
    LAYER_FLAG_RTC_OUTPUT = 12;
    LAYER_FLAG_FLAG_NOT_PREVIEW_BUT_OUTPUT = 13;
    LAYER_FLAG_ALWAYS_TOP = 14;
    LAYER_FLAG_AVOID_DESTROY_ALL = 15;
    LAYER_FLAG_RTC_NOT_OUTPUT = 16;
    LAYER_FLAG_RTC_ADD_TO_OUTPUT = 17;
	LAYER_FLAG_RTC_NOT_OUTPUT_TO_SCREEN = 18;
	LAYER_FLAG_RTC_ADD_TO_OUTPUT_SCREEN = 19;
}

message LayerInfo
{
	optional ls_base.SizeF layer_size = 1;
	optional ls_base.SizeF canvas_size = 2;
	optional ls_base.Transform transform = 3;
	optional ls_base.ClipF prepare_clip = 4;
	optional LAYER_LAYOUT layout_style = 5;
	optional bool visible = 6;
	optional bool locked = 7;
	optional LAYER_FIXED_EDGE fixed_edge = 8;
	optional ls_base.ClipF move_range = 9;
	optional ls_base.ClipF ref_layout = 10;
	repeated LAYER_FLAG visual_flags = 11;
}

message CreateLayer {
	message Request {
		string source_id = 1;
		LayerInfo layer_info = 2;
	}

	message Response {
		string layer_id = 1;
	}
}

message DestoryLayer {
	message Request {
		repeated string layer_ids = 1;
	}
}

message SetLayerInfo {
	message Request {
		string layer_id = 1;
		LayerInfo layer_info = 2;
	}
}

message GetLayerInfo {
	message Request {
		string layer_id = 1;
	}

	message Response {
		LayerInfo layer_info = 1;
	}
}

message AddFilter {
	message Request {
		string layer_id = 1;
		repeated string filter_ids = 2;
	}
}

message RemoveFilter {
	message Request {
		string layer_id = 1;
		repeated string filter_ids = 2;
	}
}

message ResetFilterOrder {
	message Request {
		string layer_id = 1;
		repeated string filter_ids = 2;
	}
}

message SelectLayer {
	message Request {
		string layer_id = 1;
	}
}

message UnSelectLayer {
	message Request {}
}

message MoveLayerOrder {
	message Request {
		string layer_id = 1;
		ls_basicenum.MOVE_ORDER move_order = 2;
	}
}

message ClipLayer {
	message Request {
		string layer_id = 1;
	}
}

message GetLayerSnapshot {
	message Request {
		string layer_id = 1;
		string path = 2;
	}
}

message GetLayerSnapshot2 {
	message Request {
		string layer_id = 1;
		string path = 2;
	}
}

message UpdateLayerSource {
	message Request {
		string layer_id = 1;
		string target_source_id = 2;
	}
}