# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# http://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library using the Microsoft GDK.

# NOTE: We use x64 MSBuild for the GDK as the NuGets don't include 32-bit support to avoid cross-arch dependencies.

schedules:
- cron: "30 3 * * *"
  displayName: 'Nightly build'
  branches:
    include:
    - main

trigger:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - CMake*
    - '.github/*'
    - '.nuget/*'
    - build/*.cmake
    - build/*.cmd
    - build/*.in
    - build/*.json
    - build/*.ps1

pr:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - CMake*
    - '.github/*'
    - '.nuget/*'
    - build/*.cmake
    - build/*.cmd
    - build/*.in
    - build/*.json
    - build/*.ps1
  drafts: false

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

pool:
  vmImage: windows-2019

variables:
  Codeql.Enabled: false
  EXTRACTED_FOLDER: $(ExtractedFolder)
  GDK_EDITION: $(GDKEditionNumber)
  GDKEnableBWOI: true
  URL_FEED: $(ADOFeedURL)
  skipNugetSecurityAnalysis: true # We explicitly call this task so we don't need it to be auto-injected

jobs:
- job: BUILD_GDK
  displayName: 'Microsoft Game Development Kit (GDK)'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
  - task: NuGetToolInstaller@1
    displayName: 'Use NuGet'
  - task: PowerShell@2
    displayName: 'Create nuget.config with single source'
    inputs:
      targetType: inline
      script: |
        $xml = @'
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
          <packageSources>
            <clear />
          </packageSources>
        </configuration>
        '@
        $xml | Set-Content -Path "$(Build.SourcesDirectory)\NuGet.config"

  - task: NuGetCommand@2
    # We have to use a nuget.config to provide the feed for the 'nuget install' option.
    displayName: 'NuGet set package source to ADO feed'
    inputs:
      command: custom
      arguments: sources add -Name xboxgdk -Source $(URL_FEED) -ConfigFile $(Build.SourcesDirectory)\NuGet.config
  - task: nuget-security-analysis@0
    displayName: 'Secure Supply Chain Analysis'
  - task: NuGetAuthenticate@1
    displayName: 'NuGet Auth'
  - task: NuGetCommand@2
    displayName: NuGet install PGDK
    inputs:
      command: custom
      arguments: install -prerelease Microsoft.GDK.PC.$(GDK_EDITION) -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: NuGetCommand@2
    displayName: NuGet install GDKX
    inputs:
      command: custom
      arguments: install -prerelease Microsoft.GDK.Xbox.$(GDK_EDITION) -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: CopyFiles@2
    displayName: Set up Directory.Build.props
    inputs:
      SourceFolder: build
      Contents: 'Directory.Build.props'
      TargetFolder: $(Build.SourcesDirectory)
  - task: MSBuild@1
    displayName: Setup BWOI VCTargets
    inputs:
      solution: build/SetupBWOI.targets
      msbuildVersion: 16.0
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 pcdbg
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Desktop.x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 pcrel
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Desktop.x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 xbdbg
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Xbox.XboxOne.x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 xbrel
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Xbox.XboxOne.x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 scardbg
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Xbox.Scarlett.x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_2019 scarrel
    continueOnError: True
    inputs:
      solution: DirectXTex_GDK_2019.sln
      vsVersion: 16.0
      platform: Gaming.Xbox.Scarlett.x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_PC_2019 dbg
    continueOnError: True
    inputs:
      solution: DirectXTex_GXDK_PC_2019.sln
      vsVersion: 16.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_PC_2019 rel
    continueOnError: True
    inputs:
      solution: DirectXTex_GXDK_PC_2019.sln
      vsVersion: 16.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_PC_2019 scardbg
    continueOnError: True
    inputs:
      solution: DirectXTex_GXDK_PC_2019.sln
      vsVersion: 16.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug_Scarlett
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_GDK_PC_2019 scarrel
    continueOnError: True
    inputs:
      solution: DirectXTex_GXDK_PC_2019.sln
      vsVersion: 16.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release_Scarlett
      msbuildArchitecture: x64
