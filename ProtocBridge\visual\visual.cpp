#include "stdafx.h"
#include "visual.h"

namespace LS
{
Visual::RequestList Visual::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Visual::Remove>());
    list.push_back(std::make_unique<Visual::IsEmpty>());
    list.push_back(std::make_unique<Visual::VisualIsReady>());

    list.push_back(std::make_unique<Visual::SetVisualUniAttr>());
    list.push_back(std::make_unique<Visual::GetVisualUniAttr>());

    list.push_back(std::make_unique<Visual::Select>());
    list.push_back(std::make_unique<Visual::UnSelect>());
    list.push_back(std::make_unique<Visual::MoveOrder>());
    list.push_back(std::make_unique<Visual::ClipVisual>());
    list.push_back(std::make_unique<Visual::GetVisualSnapshot>());
    list.push_back(std::make_unique<Visual::GetVisualSnapshot2>());
    list.push_back(std::make_unique<Visual::CreateFrame>());

    list.push_back(std::make_unique<Visual::AddFilter>());
    list.push_back(std::make_unique<Visual::RemoveFilter>());
    list.push_back(std::make_unique<Visual::ResetFilterOrder>());
    list.push_back(std::make_unique<Visual::GetEffectProfiler>());

    list.push_back(std::make_unique<Visual::StartVisualPreview>());
    list.push_back(std::make_unique<Visual::StopVisualPreview>());
    list.push_back(std::make_unique<Visual::SetVisualPreviewLayout>());
    list.push_back(std::make_unique<Visual::SetVisualPreviewParams>());

    return list;
}

bool Visual::Remove::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    for (int i = 0; i < req.visual_ids_size(); ++i)
    {
        UINT64 layerID = 0;
        Util::StringToNum(req.visual_ids(i), &layerID);
        if (!controller->FindLayerByID(layerID))
        {
            LOG(ERROR) << "[Visual::Remove] visual not exist, layerID: " << layerID;
            continue;
        }

        LAYER_INFO layerInfo{};
        controller->GetLayerInfo(layerID, &layerInfo);

        SOURCE_INFO sourceInfo{};
        controller->GetSourceInfo(layerInfo.sourceID, &sourceInfo);
        const auto& iter = std::find(sourceInfo.layerIDs.begin(), sourceInfo.layerIDs.end(), layerID);
        if (iter != sourceInfo.layerIDs.end())
        {
            sourceInfo.layerIDs.erase(iter);

            if (sourceInfo.layerIDs.size() == 0)
            {
                LOG(INFO) << "[Visual::Remove] source layerIDs is empty, destroy this source, sourceID: " << sourceInfo.id << ", sourceType: " << sourceInfo.type;
                controller->DestroySource(sourceInfo.id);

                if (sourceInfo.type == VISUAL_WINDOW)
                {
                    auto compositeMetas = controller->GetCompositeMetas();
                    for (const auto& meta : compositeMetas)
                    {
                        if (meta.second.fallbackSourceID == sourceInfo.id && meta.second.isFallback && meta.second.primaryType == VISUAL_GAME && controller->FindSourceByID(meta.second.primarySourceID))
                        {
                            LOG(INFO) << "[Visual::Remove] source layerIDs is empty, destroy game source, sourceID: " << meta.second.primarySourceID << ", sourceType: " << meta.second.primaryType;
                            controller->DestroySource(meta.second.primarySourceID);
                        }
                    }
                }
            }
            else
            {
                controller->SetSourceInfo(sourceInfo.id, &sourceInfo);
            }
        }

        controller->DeleteLayer(layerID);
    }
    
    return true;
}

bool Visual::IsEmpty::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);

    if (!controller->FindLayerByID(visualID))
    {
        rsp.set_empty(true);
        return true;
    }
    
    rsp.set_empty(false);
    return true;
}

bool Visual::VisualIsReady::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);

    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::VisualIsReady] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO layerInfo{};
    controller->GetLayerInfo(visualID, &layerInfo);
    rsp.set_is_ready(layerInfo.isReady);
    return true;
}

bool Visual::SetVisualUniAttr::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::SetVisualUniAttr] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    UINT64 uniCMD = LAYER_CONTROL_NONE;
    ls_visual::VisualUniAttr in_uni_attr = req.visual_uni_attr();
    SetVisualUniformInfo(iLayerInfo, iSourceInfo, in_uni_attr, &uniCMD);
    controller->ControlLayer(visualID, iLayerInfo, static_cast<LAYER_CONTROL_CMD>(uniCMD));

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    SOURCE_INFO_CMD cmd = SOURCE_INFO_FPS;
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo, cmd);

    CANVAS_INFO canvasInfo{};
    controller->GetCanvasInfo(oLayerInfo.canvasID, &canvasInfo);

    ls_visual::VisualUniAttr out_uni_attr{};
    GetVisualUniformInfo(canvasInfo, oLayerInfo, oSourceInfo, out_uni_attr);
    rsp.mutable_visual_uni_attr()->CopyFrom(out_uni_attr);
	return true;
}

bool Visual::GetVisualUniAttr::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::GetVisualUniAttr] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

	SOURCE_INFO oSourceInfo{};
	SOURCE_INFO_CMD cmd = SOURCE_INFO_FPS;
	controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo, cmd);

    CANVAS_INFO canvasInfo{};
    controller->GetCanvasInfo(oLayerInfo.canvasID, &canvasInfo);

    ls_visual::VisualUniAttr uni_attr{};
    GetVisualUniformInfo(canvasInfo, oLayerInfo, oSourceInfo, uni_attr);
    rsp.mutable_visual_uni_attr()->CopyFrom(uni_attr);
	return true;
}

bool Visual::Select::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::Select] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO layerInfo{};
    controller->GetLayerInfo(visualID, &layerInfo);
    controller->ControlLayer(visualID, layerInfo, LAYER_CONTROL_SELECT);
    return true;
}

bool Visual::UnSelect::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
    
    controller->UnSelectLayer();
    return true;
}

bool Visual::MoveOrder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::MoveOrder] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(visualID, &iLayerInfo);    
    controller->MoveLayerOrder(iLayerInfo.canvasID, visualID, (MOVE_ORDER)req.move_order());
    return true;
}

bool Visual::ClipVisual::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::ClipVisual] visual not exist, visualID: " << visualID;
        return false;
    }

	LAYER_INFO layerInfo{};
    controller->GetLayerInfo(visualID, &layerInfo);
    controller->ControlLayer(visualID, layerInfo, LAYER_CONTROL_SET_CLIP_MASK);
	return true;
}

bool Visual::GetVisualSnapshot::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::GetVisualSnapshot] visual not exist, visualID: " << visualID;
        return false;
    }

    controller->GetLayerSnapshot(visualID, req.path());
    return true;
}

bool Visual::GetVisualSnapshot2::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::GetVisualSnapshot2] visual not exist, visualID: " << visualID;
        return false;
    }

    controller->GetLayerSnapshot2(visualID, req.path());
    return true;
}

bool Visual::CreateFrame::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 mediaID = 0;
    Util::StringToNum(req.media_id(), &mediaID);
    Gdiplus::SizeF size;
    if (req.has_resize())
    {
        size.Width = req.resize().x();
        size.Height = req.resize().y();
    }

    CLIPF clip;
    if (req.has_clip())
    {
        clip.x = req.clip().x();
        clip.y = req.clip().y();
        clip.z = req.clip().z();
        clip.w = req.clip().w();
    }

    OBJECT_FIT_MODE fitMode = (OBJECT_FIT_MODE)req.fit_mode();

    UINT64         frameID = 0;
    Gdiplus::SizeF newSize;
    controller->CreateFrame(mediaID, size, clip, fitMode, &frameID);

    std::string frame_id = "";
    Util::NumToString(frameID, &frame_id);
    rsp.set_frame_id(frame_id);
    return true;
}

bool Visual::AddFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::AddFilter] visual not exist, visualID: " << visualID;
        return false;
    }

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Visual::AddFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    controller->AddFilter(visualID, filterID);
    return true;
}

bool Visual::RemoveFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::RemoveFilter] visual not exist, visualID: " << visualID;
        return true;
    }

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Visual::RemoveFilter] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->RemoveFilter(visualID, filterID);
    return true;
}

bool Visual::ResetFilterOrder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::ResetFilterOrder] visual not exist, visualID: " << visualID;
        return false;
    }

    std::vector<std::string> filterIDs{};
    for (int i = 0; i < req.filter_ids_size(); ++i)
    {
        UINT64 filterID = 0;
        Util::StringToNum(req.filter_ids(i), &filterID);
        if (!controller->FindFilterByID(filterID))
        {
            LOG(ERROR) << "[Visual::ResetFilterOrder] filter not exist, filterID: " << filterID;
            continue;
        }
        filterIDs.push_back(req.filter_ids(i));
    }
    controller->ResetFilterOrder(visualID, filterIDs);
    return true;
}

bool Visual::GetEffectProfiler::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::GetEffectProfiler] visual not exist, visualID: " << visualID;
        return false;
    }

    EffectProfilerInfo effect_profiler_info{};

    LAYER_INFO layerInfo{};
    controller->GetLayerInfo(visualID, &layerInfo);
    controller->GetEffectProfiler(layerInfo.id, layerInfo.sourceID, &effect_profiler_info);

    rsp.set_before_effect_fps(effect_profiler_info.before_effect_fps);
    rsp.set_after_effect_fps(effect_profiler_info.after_effect_fps);
    rsp.set_effect_achieve_rate(effect_profiler_info.effect_achieve_rate);
    rsp.set_tick_achieve_rate(effect_profiler_info.tick_achieve_rate);

    return true;
}

void Visual::SetPreviewAttr(LAYER_PREVIEW& preview, const ls_visual::VisualPreviewAttr& preview_attr)
{
    if (preview_attr.has_size())
    {
        preview.size.Width = preview_attr.size().x();
        preview.size.Height = preview_attr.size().y();
    }

    if (preview_attr.has_transform())
    {
        ls_base::Transform transform = preview_attr.transform();
        if (transform.has_flip_h())
        {
            preview.transform.hFlip = transform.flip_h();
        }
        if (transform.has_flip_v())
        {
            preview.transform.vFlip = transform.flip_v();
        }
        if (transform.has_angle())
        {
            preview.transform.angle = transform.angle();
        }
        if (transform.has_scale())
        {
            preview.transform.scale.X = transform.scale().x();
            preview.transform.scale.Y = transform.scale().y();
        }
        if (transform.has_translate())
        {
            preview.transform.translate.X = transform.translate().x();
            preview.transform.translate.Y = transform.translate().y();
        }
        if (transform.has_size())
        {
            preview.transform.size.Width = transform.size().x();
            preview.transform.size.Height = transform.size().y();
        }
        else
        {
            preview.transform.size.Width = preview.size.Width;
            preview.transform.size.Height = preview.size.Height;
        }
    }

    if (preview_attr.has_bk_color())
    {
        preview.bkColor = preview_attr.bk_color();
    }

    if (preview_attr.has_fill_type())
    {
        preview.fillType = (VISUAL_PREVIEW_FILL)preview_attr.fill_type();
    }
}

bool Visual::StartVisualPreview::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::StartVisualPreview] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_PREVIEW preview{};
    Visual::SetPreviewAttr(preview, req.preview_attr());

    PREVIEW_PARAMS params = { req.is_popup(), req.top_border_radius(), req.bottom_border_radius(), req.opacity() };
    UINT64 previewID = 0;
    if (controller->StartLayerPreview(visualID, req.parent_hwnd(), &preview, &previewID, params))
    {
        std::string preview_id = "";
        Util::NumToString(previewID, &preview_id);
        rsp.set_visual_preview_id(preview_id);
        rsp.set_error_code(0);
    }
    else
    {
        rsp.set_visual_preview_id("");
        rsp.set_error_code(-1);
    }
    return true;
}

bool Visual::StopVisualPreview::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::StopVisualPreview] visual not exist, visualID: " << visualID;
        return false;
    }
    UINT64 previewID = 0;
    Util::StringToNum(req.visual_preview_id(), &previewID);
    if (controller->StopLayerPreview(visualID, previewID))
    {
        rsp.set_error_code(0);
    }
    else
    {
        rsp.set_error_code(-1);
    }
    return true;
}

bool Visual::SetVisualPreviewParams::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
    PREVIEW_PARAMS params = { true, req.top_border_radius(), req.bottom_border_radius(), req.opacity() };
    if (!controller->SetLayerPreviewParams(req.visual_id(), req.visual_preview_id(), &params))
    {
        rsp.set_error_code(-1);
    }
    else
    {
        rsp.set_error_code(0);
    }
    return true;
}

bool Visual::SetVisualPreviewLayout::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Visual::SetVisualPreviewLayout] visual not exist, visualID: " << visualID;
        return false;
    }

    UINT64 previewID = 0;
    Util::StringToNum(req.visual_preview_id(), &previewID);

    LAYER_PREVIEW preview{};
    controller->GetLayerPreview(visualID, previewID, &preview);

    Visual::SetPreviewAttr(preview, req.preview_attr());
    if (controller->SetLayerPreviewLayout(visualID, previewID, &preview))
    {
        rsp.set_error_code(0);
    }
    else
    {
        rsp.set_error_code(-1);
    }
    return true;
}

void Visual::SetVisualUniformInfo(LAYER_INFO& layerInfo, SOURCE_INFO& sourceInfo, const ls_visual::VisualUniAttr& attr, UINT64* cmd)
{
    if (attr.has_visible())
    {
        layerInfo.show = attr.visible();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_SHOW;
    }
    if (attr.has_locked())
    {
        layerInfo.locked = attr.locked();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_LOCK;
    }
    if (attr.has_size())
    {
        layerInfo.transform.size.Width = attr.size().x();
        layerInfo.transform.size.Height = attr.size().y();
    }
    if (attr.has_canvas_size())
    {
        layerInfo.targetSize.Width = attr.canvas_size().x();
        layerInfo.targetSize.Height = attr.canvas_size().y();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_TARGET_SIZE;
    }
    if (attr.has_ref_layout())
    {
        layerInfo.refLayout.x = attr.ref_layout().x();
        layerInfo.refLayout.y = attr.ref_layout().y();
        layerInfo.refLayout.z = attr.ref_layout().z();
        layerInfo.refLayout.w = attr.ref_layout().w();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_REF_LAYOUT;
    }
    if (attr.has_layout())
    {
        layerInfo.layout = (LAYER_LAYOUT)attr.layout();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_LAYOUT;
    }
    if (attr.has_fixed_edge())
    {
        layerInfo.fixedEdge = (LAYER_FIXED_EDGE)attr.fixed_edge();
    }
    if (attr.has_move_range())
    {
        CLIPF moveRange;
        moveRange.x = attr.move_range().x();
        moveRange.y = attr.move_range().y();
        moveRange.z = attr.move_range().z();
        moveRange.w = attr.move_range().w();
        layerInfo.moveRange = moveRange;
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_MOVE_RANGE;
    }

    if (attr.has_transform())
    {
        TRANSFORM&         sourceTrans = layerInfo.transform;
        ls_base::Transform visualTrans = attr.transform();
        if (visualTrans.has_flip_h())
        {
            sourceTrans.hFlip = visualTrans.flip_h();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_FLIPH;
        }
        if (visualTrans.has_flip_v())
        {
            sourceTrans.vFlip = visualTrans.flip_v();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_FLIPV;
        }
        if (visualTrans.has_angle())
        {
            sourceTrans.angle = visualTrans.angle();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_ROTATE;
        }
        if (visualTrans.has_scale())
        {
            sourceTrans.scale.X = visualTrans.scale().x();
            sourceTrans.scale.Y = visualTrans.scale().y();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_SCALE;
        }
        if (visualTrans.has_min_scale())
        {
            sourceTrans.minScale.X = visualTrans.min_scale().x();
            sourceTrans.minScale.Y = visualTrans.min_scale().y();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_MIN_SCALE;
        }
        if (visualTrans.has_max_scale())
        {
            sourceTrans.maxScale.X = visualTrans.max_scale().x();
            sourceTrans.maxScale.Y = visualTrans.max_scale().y();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_MAX_SCALE;
        }
        if (visualTrans.has_translate())
        {
            sourceTrans.translate.X = visualTrans.translate().x();
            sourceTrans.translate.Y = visualTrans.translate().y();

            if (cmd)
                *cmd |= LAYER_CONTROL_SET_TRANSLATE;
        }
        if (visualTrans.has_clip())
        {
            sourceTrans.clipRange.x = visualTrans.clip().x();
            sourceTrans.clipRange.y = visualTrans.clip().y();
            sourceTrans.clipRange.z = visualTrans.clip().z();
            sourceTrans.clipRange.w = visualTrans.clip().w();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_CLIP;
        }
    }
    if (attr.has_prepare_clip())
    {
        layerInfo.prepareClip.x = attr.prepare_clip().x();
        layerInfo.prepareClip.y = attr.prepare_clip().y();
        layerInfo.prepareClip.z = attr.prepare_clip().z();
        layerInfo.prepareClip.w = attr.prepare_clip().w();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_PREPARE_CLIP;
    }

    if (!attr.visual_flags().empty())
    {
        for (int i = 0; i < attr.visual_flags_size(); ++i)
        {
            VISUAL_FLAG flag = (VISUAL_FLAG)attr.visual_flags(i);
            auto it = std::find(layerInfo.visualFlags.begin(), layerInfo.visualFlags.end(), flag);
            if (it == layerInfo.visualFlags.end())
            {
                layerInfo.visualFlags.push_back(flag);
            }
        }

        if (cmd && !layerInfo.visualFlags.empty())
            *cmd |= LAYER_CONTROL_SET_VISUAL_FLAGS;
    }

    if (attr.has_fps())
    {
        sourceInfo.fps = attr.fps();
    }
}

void Visual::GetVisualUniformInfo(const CANVAS_INFO& canvasInfo, const LAYER_INFO& layerInfo, const SOURCE_INFO& sourceInfo, ls_visual::VisualUniAttr& attr)
{
    attr.set_type((ls_visual::VISUAL_TYPE)sourceInfo.type);
    attr.set_fps(sourceInfo.fps);

    ls_base::SizeF target_size{};
    target_size.set_x(canvasInfo.layoutRect.Width);
    target_size.set_y(canvasInfo.layoutRect.Height);
    attr.mutable_canvas_size()->CopyFrom(target_size);

    attr.set_layout((ls_visual::VISUAL_LAYOUT)layerInfo.layout);
    attr.set_fixed_edge((ls_visual::VISUAL_FIXED_EDGE)layerInfo.fixedEdge);
    attr.set_visible(layerInfo.show);
    attr.set_locked(layerInfo.locked);

    for (const VISUAL_FLAG& flag : layerInfo.visualFlags)
    {
        attr.add_visual_flags((ls_visual::VISUAL_FLAG)flag);
    }
    ls_base::ClipF move_range{};
    move_range.set_x(layerInfo.moveRange.x);
    move_range.set_y(layerInfo.moveRange.y);
    move_range.set_z(layerInfo.moveRange.z);
    move_range.set_w(layerInfo.moveRange.w);
    attr.mutable_move_range()->CopyFrom(move_range);

    ls_base::SizeF size{};
    size.set_x(layerInfo.transform.size.Width);
    size.set_y(layerInfo.transform.size.Height);
    attr.mutable_size()->CopyFrom(size);

    ls_base::ClipF prepare_clip{};
    prepare_clip.set_x(layerInfo.prepareClip.x);
    prepare_clip.set_y(layerInfo.prepareClip.y);
    prepare_clip.set_z(layerInfo.prepareClip.z);
    prepare_clip.set_w(layerInfo.prepareClip.w);
    attr.mutable_prepare_clip()->CopyFrom(prepare_clip);

    ls_base::Transform transform{};
    TRANSFORM          sourceTrans = layerInfo.transform;
    transform.set_flip_h(sourceTrans.hFlip);
    transform.set_flip_v(sourceTrans.vFlip);
    transform.set_angle(sourceTrans.angle);

    ls_base::ScaleF scale{};
    scale.set_x(sourceTrans.scale.X);
    scale.set_y(sourceTrans.scale.Y);
    transform.mutable_scale()->CopyFrom(scale);

    ls_base::ScaleF min_scale{};
    min_scale.set_x(sourceTrans.minScale.X);
    min_scale.set_y(sourceTrans.minScale.Y);
    transform.mutable_min_scale()->CopyFrom(min_scale);

    ls_base::ScaleF max_scale{};
    max_scale.set_x(sourceTrans.maxScale.X);
    max_scale.set_y(sourceTrans.maxScale.Y);
    transform.mutable_max_scale()->CopyFrom(max_scale);

    ls_base::TranslateF translate{};
    translate.set_x(sourceTrans.translate.X);
    translate.set_y(sourceTrans.translate.Y);
    transform.mutable_translate()->CopyFrom(translate);

    ls_base::ClipF clip{};
    clip.set_x(sourceTrans.clipRange.x);
    clip.set_y(sourceTrans.clipRange.y);
    clip.set_z(sourceTrans.clipRange.z);
    clip.set_w(sourceTrans.clipRange.w);
    transform.mutable_clip()->CopyFrom(clip);

    ls_base::SizeF trans_size{};
    trans_size.set_x(sourceTrans.size.Width);
    trans_size.set_y(sourceTrans.size.Height);
    transform.mutable_size()->CopyFrom(trans_size);
    attr.mutable_transform()->CopyFrom(transform);
}
} // namespace MediaSDK
