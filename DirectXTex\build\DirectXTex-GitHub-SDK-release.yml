# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# http://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library using the latest release of the Windows SDK from nuget.org.

schedules:
- cron: "0 3 * * 0"
  displayName: 'Sunday night build'
  branches:
    include:
    - main
  always: true

trigger: none
pr: none

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

variables:
  Codeql.Enabled: false
  EXTRACTED_FOLDER: $(ExtractedFolder)
  WSDKEnableBWOI: true
  URL_FEED: $(ADOFeedURL)
  skipNugetSecurityAnalysis: true # We explicitly call this task so we don't need it to be auto-injected

pool:
  vmImage: windows-2022

jobs:
- job: DESKTOP_BUILD
  displayName: 'Win32 Desktop'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
  - task: NuGetToolInstaller@1
    displayName: 'Use NuGet'
  - task: PowerShell@2
    displayName: 'Create nuget.config with single source'
    inputs:
      targetType: inline
      script: |
        $xml = @'
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
          <packageSources>
            <clear />
          </packageSources>
        </configuration>
        '@
        $xml | Set-Content -Path "$(Build.SourcesDirectory)\NuGet.config"

  - task: NuGetCommand@2
    # We have to use a nuget.config to provide the feed for the 'nuget install' option.
    displayName: 'NuGet set package source to ADO feed'
    inputs:
      command: custom
      arguments: sources add -Name xboxgdk -Source $(URL_FEED) -ConfigFile $(Build.SourcesDirectory)\NuGet.config
  - task: nuget-security-analysis@0
    displayName: 'Secure Supply Chain Analysis'
  - task: NuGetAuthenticate@1
    displayName: 'NuGet Auth'
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK x64
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.x64 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK x86
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.x86 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK arm64
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.arm64 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: CopyFiles@2
    displayName: Set up Directory.Build.props
    inputs:
      SourceFolder: build
      Contents: 'Directory.Build.props'
      TargetFolder: $(Build.SourcesDirectory)
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019.sln 32dbg
    inputs:
      solution: DirectXTex_Desktop_2019.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019.sln 32rel
    inputs:
      solution: DirectXTex_Desktop_2019.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Release
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019.sln 64dbg
    inputs:
      solution: DirectXTex_Desktop_2019.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019.sln 64rel
    inputs:
      solution: DirectXTex_Desktop_2019.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019_Win10.sln 32dbg
    inputs:
      solution: DirectXTex_Desktop_2019_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019_Win10.sln 32rel
    inputs:
      solution: DirectXTex_Desktop_2019_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Release
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019_Win10.sln 64dbg
    inputs:
      solution: DirectXTex_Desktop_2019_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2019_Win10.sln 64rel
    inputs:
      solution: DirectXTex_Desktop_2019_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
  # VS 2019 for Win32 on ARM64 is out of support.
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2022_Win10.sln arm64dbg
    inputs:
      solution: DirectXTex_Desktop_2022_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Desktop_2022_Win10.sln arm64rel
    inputs:
      solution: DirectXTex_Desktop_2022_Win10.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Release

- job: UWP_BUILD
  displayName: 'Universal Windows Platform (UWP)'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
  - task: NuGetToolInstaller@1
    displayName: 'Use NuGet'
  - task: PowerShell@2
    displayName: 'Create nuget.config with single source'
    inputs:
      targetType: inline
      script: |
        $xml = @'
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
          <packageSources>
            <clear />
          </packageSources>
        </configuration>
        '@
        $xml | Set-Content -Path "$(Build.SourcesDirectory)\NuGet.config"

  - task: NuGetCommand@2
    displayName: NuGet set package source to ADO feed
    inputs:
      command: custom
      arguments: sources add -Name xboxgdk -Source $(URL_FEED) -ConfigFile $(Build.SourcesDirectory)\NuGet.config
  - task: nuget-security-analysis@0
    displayName: 'Secure Supply Chain Analysis'
  - task: NuGetAuthenticate@1
    displayName: 'NuGet Auth'
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK x64
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.x64 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK x86
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.x86 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: NuGetCommand@2
    displayName: NuGet Install WSDK arm64
    inputs:
      command: custom
      arguments: install Microsoft.Windows.SDK.CPP.arm64 -ExcludeVersion -OutputDirectory $(EXTRACTED_FOLDER)
  - task: CopyFiles@2
    displayName: Set up Directory.Build.props
    inputs:
      SourceFolder: build
      Contents: 'Directory.Build.props'
      TargetFolder: $(Build.SourcesDirectory)
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln 32dbg
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln 32rel
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Release
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln 64dbg
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln 64rel
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
  # Windows on ARM 32-bit is deprecated. https://learn.microsoft.com/windows/arm/arm32-to-arm64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln arm64dbg
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Debug
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Windows10_2022.sln arm64rel
    inputs:
      solution: DirectXTex_Windows10_2022.sln
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Release
