#pragma once

#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_visual.pb.h"

namespace LS
{

class Visual : public PB::Module
{
private:
    virtual RequestList GetRequestHandlers() const override;

public:
    struct Remove : public PB::RequestHandler<ls_visual::Remove>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct IsEmpty : public PB::RequestHandler<ls_visual::IsEmpty>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct VisualIsReady : public PB::RequestHandler<ls_visual::VisualIsReady>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct SetVisualUniAttr : public PB::RequestHandler<ls_visual::SetVisualUniAttr>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct GetVisualUniAttr : public PB::RequestHandler<ls_visual::GetVisualUniAttr>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct Select : public PB::RequestHandler<ls_visual::Select>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct UnSelect : public PB::RequestHandler<ls_visual::UnSelect>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct MoveOrder : public PB::RequestHandler<ls_visual::MoveOrder>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct ClipVisual : public PB::RequestHandler<ls_visual::ClipVisual>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct GetVisualSnapshot : public PB::RequestHandler<ls_visual::GetVisualSnapshot>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct GetVisualSnapshot2 : public PB::RequestHandler<ls_visual::GetVisualSnapshot2>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct CreateFrame : public PB::RequestHandler<ls_visual::CreateFrame>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct AddFilter : public PB::RequestHandler<ls_visual::AddFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveFilter : public PB::RequestHandler<ls_visual::RemoveFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct ResetFilterOrder : public PB::RequestHandler<ls_visual::ResetFilterOrder>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct GetEffectProfiler : public PB::RequestHandler<ls_visual::GetEffectProfiler>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	struct StartVisualPreview : public PB::RequestHandler<ls_visual::StartVisualPreview>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct StopVisualPreview : public PB::RequestHandler<ls_visual::StopVisualPreview>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct SetVisualPreviewParams : public PB::RequestHandler<ls_visual::SetVisualPreviewParams>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct SetVisualPreviewLayout : public PB::RequestHandler<ls_visual::SetVisualPreviewLayout>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

	static void SetVisualUniformInfo(LAYER_INFO& layerInfo, SOURCE_INFO& sourceInfo, const ls_visual::VisualUniAttr& attr, UINT64* cmd = NULL);
	static void GetVisualUniformInfo(const CANVAS_INFO& canvasInfo, const LAYER_INFO& layerInfo, const SOURCE_INFO& sourceInfo, ls_visual::VisualUniAttr& attr);
	static void SetPreviewAttr(LAYER_PREVIEW& preview, const ls_visual::VisualPreviewAttr& preview_attr);
};
} // namespace MediaSDK
