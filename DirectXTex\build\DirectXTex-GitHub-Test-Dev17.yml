# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# https://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library and test suite.

schedules:
- cron: "30 3 * * *"
  displayName: 'Nightly build'
  branches:
    include:
    - main

# GitHub Actions handles test suite for CI/PR
trigger: none
pr:
  branches:
    include:
    - main
  paths:
    include:
    - build/DirectXTex-GitHub-Test-Dev17.yml

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main
    trigger: none
  - repository: testRepo
    name: walbourn/directxtextest
    type: github
    endpoint: microsoft
    ref: refs/heads/main

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

pool:
  vmImage: windows-2022

variables:
  Codeql.Enabled: false
  VC_PATH: 'C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC'
  GUID_FEED: $(ADOFeedGUID)

jobs:
- job: DESKTOP_BUILD
  displayName: 'Win32 Desktop for x64/x86'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: testRepo
    displayName: Fetch Tests
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/Tests'
  - task: NuGetToolInstaller@1
    displayName: 'Use NuGet'
  - task: NuGetCommand@2
    displayName: NuGet restore tests
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022.sln
      feedRestore: $(GUID_FEED)
      includeNuGetOrg: false
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022.sln 32dbg
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022.sln 32rel
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022.sln 64dbg
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022.sln 64rel
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln 32dbg
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln 32rel
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x86
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln 64dbg
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln 64rel
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: x64
      configuration: Release
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln arm64dbg
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Debug
      msbuildArchitecture: x64
  - task: VSBuild@1
    displayName: Build solution DirectXTex_Tests_Desktop_2022_Win10.sln arm64rel
    inputs:
      solution: Tests/DirectXTex_Tests_Desktop_2022_Win10.sln
      vsVersion: 17.0
      msbuildArgs: /p:PreferredToolArchitecture=x64
      platform: ARM64
      configuration: Release
      msbuildArchitecture: x64

- job: CMAKE_BUILD_X64
  displayName: 'CMake for X64 BUILD_TESTING=ON'
  timeoutInMinutes: 120
  workspace:
    clean: all
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: testRepo
    displayName: Fetch Tests
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/Tests'
  - task: CmdLine@2
    displayName: Setup environment for CMake to use VS
    inputs:
      script: |
        call "$(VC_PATH)\Auxiliary\Build\vcvars64.bat"
        echo ##vso[task.setvariable variable=WindowsSdkVerBinPath;]%WindowsSdkVerBinPath%
        echo ##vso[task.prependpath]%VSINSTALLDIR%Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja
        echo ##vso[task.prependpath]%VCINSTALLDIR%Tools\Llvm\x64\bin
        echo ##vso[task.prependpath]%WindowsSdkBinPath%x64
        echo ##vso[task.prependpath]%WindowsSdkVerBinPath%x64
        echo ##vso[task.prependpath]%VCToolsInstallDir%bin\Hostx64\x64
        echo ##vso[task.setvariable variable=EXTERNAL_INCLUDE;]%EXTERNAL_INCLUDE%
        echo ##vso[task.setvariable variable=INCLUDE;]%INCLUDE%
        echo ##vso[task.setvariable variable=LIB;]%LIB%

  - task: CMake@1
    displayName: CMake (MSVC; x64-Debug) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=x64-Debug
  - task: CMake@1
    displayName: CMake (MSVC; x64-Debug) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/x64-Debug -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (MSVC; x64-Release) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=x64-Release
  - task: CMake@1
    displayName: CMake (MSVC; x64-Release) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/x64-Release -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (clang/LLVM; x64-Debug) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=x64-Debug-Clang
  - task: CMake@1
    displayName: CMake (clang/LLVM; x64-Debug) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/x64-Debug-Clang -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (clang/LLVM; x64-Release) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=x64-Release-Clang
  - task: CMake@1
    displayName: CMake (clang/LLVM; x64-Release) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/x64-Release-Clang -v

- job: CMAKE_BUILD_ARM64
  displayName: 'CMake for ARM64 BUILD_TESTING=ON'
  timeoutInMinutes: 120
  workspace:
    clean: all
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: testRepo
    displayName: Fetch Tests
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/Tests'
  - task: CmdLine@2
    displayName: Setup environment for CMake to use VS
    inputs:
      script: |
        call "$(VC_PATH)\Auxiliary\Build\vcvarsamd64_arm64.bat"
        echo ##vso[task.setvariable variable=WindowsSdkVerBinPath;]%WindowsSdkVerBinPath%
        echo ##vso[task.prependpath]%VSINSTALLDIR%Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja
        echo ##vso[task.prependpath]%VCINSTALLDIR%Tools\Llvm\x64\bin
        echo ##vso[task.prependpath]%WindowsSdkBinPath%x64
        echo ##vso[task.prependpath]%WindowsSdkVerBinPath%x64
        echo ##vso[task.prependpath]%VCToolsInstallDir%bin\Hostx64\arm64
        echo ##vso[task.setvariable variable=EXTERNAL_INCLUDE;]%EXTERNAL_INCLUDE%
        echo ##vso[task.setvariable variable=INCLUDE;]%INCLUDE%
        echo ##vso[task.setvariable variable=LIB;]%LIB%

  - task: CMake@1
    displayName: CMake (MSVC; arm64-Debug) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64-Debug
  - task: CMake@1
    displayName: CMake (MSVC; arm64-Debug) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64-Debug -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (MSVC; arm64-Release) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64-Release
  - task: CMake@1
    displayName: CMake (MSVC; arm64-Release) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64-Release -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (clang/LLVM; arm64-Debug) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64-Debug-Clang
  - task: CMake@1
    displayName: CMake (clang/LLVM; arm64-Debug) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64-Debug-Clang -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (clang/LLVM; arm64-Release) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64-Release-Clang
  - task: CMake@1
    displayName: CMake (clang/LLVM; arm64-Release) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64-Release-Clang -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (MSVC; arm64ec-Debug) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64ec-Debug
  - task: CMake@1
    displayName: CMake (MSVC; arm64ec-Debug) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64ec-Debug -v
  - task: DeleteFiles@1
    inputs:
      Contents: 'out'
  - task: CMake@1
    displayName: CMake (MSVC; arm64ec-Release) Config
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --preset=arm64ec-Release
  - task: CMake@1
    displayName: CMake (MSVC; arm64ec-Release) Build
    inputs:
      cwd: '$(Build.SourcesDirectory)'
      cmakeArgs: --build out/build/arm64ec-Release -v
