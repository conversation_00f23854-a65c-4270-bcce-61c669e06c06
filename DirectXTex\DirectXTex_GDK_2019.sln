﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30404.54
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DirectXTex_GDK_2019", "DirectXTex\DirectXTex_GDK_2019.vcxproj", "{E66DD892-857B-4E89-B135-5E2A971A9933}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{AB602393-CA4A-4D64-913E-E37D85A6CC6D}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Gaming.Desktop.x64 = Debug|Gaming.Desktop.x64
		Debug|Gaming.Xbox.Scarlett.x64 = Debug|Gaming.Xbox.Scarlett.x64
		Debug|Gaming.Xbox.XboxOne.x64 = Debug|Gaming.Xbox.XboxOne.x64
		Profile|Gaming.Desktop.x64 = Profile|Gaming.Desktop.x64
		Profile|Gaming.Xbox.Scarlett.x64 = Profile|Gaming.Xbox.Scarlett.x64
		Profile|Gaming.Xbox.XboxOne.x64 = Profile|Gaming.Xbox.XboxOne.x64
		Release|Gaming.Desktop.x64 = Release|Gaming.Desktop.x64
		Release|Gaming.Xbox.Scarlett.x64 = Release|Gaming.Xbox.Scarlett.x64
		Release|Gaming.Xbox.XboxOne.x64 = Release|Gaming.Xbox.XboxOne.x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Desktop.x64.ActiveCfg = Debug|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Desktop.x64.Build.0 = Debug|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Xbox.Scarlett.x64.ActiveCfg = Debug|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Xbox.Scarlett.x64.Build.0 = Debug|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Xbox.XboxOne.x64.ActiveCfg = Debug|Gaming.Xbox.XboxOne.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Debug|Gaming.Xbox.XboxOne.x64.Build.0 = Debug|Gaming.Xbox.XboxOne.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Desktop.x64.ActiveCfg = Profile|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Desktop.x64.Build.0 = Profile|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Xbox.Scarlett.x64.ActiveCfg = Profile|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Xbox.Scarlett.x64.Build.0 = Profile|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Xbox.XboxOne.x64.ActiveCfg = Profile|Gaming.Xbox.XboxOne.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Profile|Gaming.Xbox.XboxOne.x64.Build.0 = Profile|Gaming.Xbox.XboxOne.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Desktop.x64.ActiveCfg = Release|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Desktop.x64.Build.0 = Release|Gaming.Desktop.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Xbox.Scarlett.x64.ActiveCfg = Release|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Xbox.Scarlett.x64.Build.0 = Release|Gaming.Xbox.Scarlett.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Xbox.XboxOne.x64.ActiveCfg = Release|Gaming.Xbox.XboxOne.x64
		{E66DD892-857B-4E89-B135-5E2A971A9933}.Release|Gaming.Xbox.XboxOne.x64.Build.0 = Release|Gaming.Xbox.XboxOne.x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {40D61785-B6CB-4A60-89E6-544E89B61BE0}
	EndGlobalSection
EndGlobal
