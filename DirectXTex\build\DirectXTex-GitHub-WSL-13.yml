# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# https://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library for Windows Subsystem for Linux (WSL)

schedules:
- cron: "0 3 * * *"
  displayName: 'Nightly build'
  branches:
    include:
    - main

trigger: none

pr:
  branches:
    include:
    - main
  paths:
    include:
    - CMake*
    - build/*.cmake
    - build/*.in
    - build/DirectXTex-GitHub-WSL-13.yml
    - Auxiliary/*EXR*
    - Auxiliary/*JPEG*
    - Auxiliary/*PNG*

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main
    trigger: none
  - repository: dxHeadersRepo
    name: Microsoft/DirectX-Headers
    type: github
    endpoint: microsoft
    ref: refs/heads/main
  - repository: dxMathRepo
    name: Microsoft/DirectXMath
    type: github
    endpoint: microsoft
    ref: refs/heads/main
  - repository: vcpkgRepo
    name: Microsoft/vcpkg
    type: github
    endpoint: microsoft
    ref: refs/tags/$(VCPKG_TAG)

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

pool:
    vmImage: ubuntu-24.04

variables:
  Codeql.Enabled: false
  VCPKG_ROOT: $(Build.SourcesDirectory)/vcpkg
  VCPKG_CMAKE_DIR: $(Build.SourcesDirectory)/vcpkg/scripts/buildsystems/vcpkg.cmake
  VCPKG_MANIFEST_DIR: $(Build.SourcesDirectory)/build
  LOCAL_PKG_DIR: '$(Agent.BuildDirectory)/install/'

jobs:
- job: BUILD_WSL
  displayName: 'Windows Subsystem for Linux (WSL)'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: dxHeadersRepo
    displayName: Fetch DirectX-Headers
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/DirectX-Headers'
  - checkout: dxMathRepo
    displayName: Fetch DirectX-Math
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/DirectXMath'
  - task: CMake@1
    displayName: CMake DirectX-Headers
    inputs:
      cwd: DirectX-Headers
      cmakeArgs: . -DDXHEADERS_BUILD_TEST=OFF -DDXHEADERS_BUILD_GOOGLE_TEST=OFF -DCMAKE_INSTALL_PREFIX=$(LOCAL_PKG_DIR)
  - task: CMake@1
    displayName: CMake DirectX-Headers (Build)
    inputs:
      cwd: DirectX-Headers
      cmakeArgs: --build . -v
  - task: CMake@1
    displayName: CMake DirectX-Headers (Install)
    inputs:
      cwd: DirectX-Headers
      cmakeArgs: --install .
  - task: CMake@1
    displayName: CMake DirectXMath
    inputs:
      cwd: DirectXMath
      cmakeArgs: . -DCMAKE_INSTALL_PREFIX=$(LOCAL_PKG_DIR)
  - task: CMake@1
    displayName: CMake DirectXMath (Build)
    inputs:
      cwd: DirectXMath
      cmakeArgs: --build . -v
  - task: CMake@1
    displayName: CMake DirectXMath (Install)
    inputs:
      cwd: DirectXMath
      cmakeArgs: --install .
  - task: PowerShell@2
    displayName: Fetch SAL.H
    inputs:
      targetType: inline
      script: |
        $ProgressPreference = 'SilentlyContinue'
        Invoke-WebRequest -Uri https://raw.githubusercontent.com/dotnet/corert/master/src/Native/inc/unix/sal.h -OutFile $(LOCAL_PKG_DIR)/include/sal.h
        $fileHash = Get-FileHash -Algorithm SHA512 $(LOCAL_PKG_DIR)/include/sal.h | ForEach { $_.Hash} | Out-String
        $filehash = $fileHash.Trim()
        Write-Host "##[debug]SHA512: " $filehash
        if ($fileHash -ne "1643571673195d9eb892d2f2ac76eac7113ef7aa0ca116d79f3e4d3dc9df8a31600a9668b7e7678dfbe5a76906f9e0734ef8d6db0903ccc68fc742dd8238d8b0") {
            Write-Error -Message "##[error]Computed hash does not match!" -ErrorAction Stop
        }

  - task: CMake@1
    displayName: CMake DirectXTex (Config) dbg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: -B out -DCMAKE_BUILD_TYPE=Debug -DCMAKE_PREFIX_PATH=$(LOCAL_PKG_DIR)/share;$(LOCAL_PKG_DIR)/cmake
  - task: CMake@1
    displayName: CMake DirectXTex (Build) dbg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out -v
  - task: CMake@1
    displayName: CMake DirectXTex (Config) rel
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: -B out2 -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=$(LOCAL_PKG_DIR)/share;$(LOCAL_PKG_DIR)/cmake
  - task: CMake@1
    displayName: CMake DirectXTex (Build) rel
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2 -v

- job: BUILD_WSL_VCPKG
  displayName: 'Windows Subsystem for Linux (WSL) using VCPKG'
  timeoutInMinutes: 120
  cancelTimeoutInMinutes: 1
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: vcpkgRepo
    displayName: Fetch VCPKG
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/vcpkg'
  - task: Bash@3
    displayName: VCPKG Bootstrap
    inputs:
      targetType: filePath
      filePath: $(Build.SourcesDirectory)/vcpkg/bootstrap-vcpkg.sh
      workingDirectory: $(Build.SourcesDirectory)/vcpkg
  - task: CmdLine@2
    displayName: GCC and CMake version
    inputs:
      script: |
        g++ --version
        cmake --version

  - task: CmdLine@2
    displayName: VCPKG install packages
    inputs:
      script: vcpkg install --x-manifest-root=$(VCPKG_MANIFEST_DIR) --triplet=x64-linux
      workingDirectory: $(VCPKG_ROOT)
  - task: CMake@1
    displayName: CMake DirectXTex (Config) dbg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out -DCMAKE_BUILD_TYPE=Debug -DDIRECTX_ARCH=x64
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-linux
  - task: CMake@1
    displayName: CMake DirectXTex (Build) dbg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out -v
  - task: CMake@1
    displayName: CMake DirectXTex (Config) rel
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out2 -DCMAKE_BUILD_TYPE=Release -DDIRECTX_ARCH=x64
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-linux
  - task: CMake@1
    displayName: CMake DirectXTex (Build) rel
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2 -v
  - task: CMake@1
    displayName: CMake DirectXTex (Config) w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out3 -DCMAKE_BUILD_TYPE=Debug -DENABLE_OPENEXR_SUPPORT=ON -DDIRECTX_ARCH=x64
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-linux
  - task: CMake@1
    displayName: CMake DirectXTex (Build) w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out3 -v
  - task: CMake@1
    displayName: CMake DirectXTex (Config) w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out4 -DCMAKE_BUILD_TYPE=Debug -DENABLE_LIBJPEG_SUPPORT=ON -DDIRECTX_ARCH=x64
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-linux
  - task: CMake@1
    displayName: CMake DirectXTex (Build) w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4 -v
  - task: CMake@1
    displayName: CMake DirectXTex (Config) w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out5 -DCMAKE_BUILD_TYPE=Debug -DENABLE_LIBPNG_SUPPORT=ON -DDIRECTX_ARCH=x64
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-linux
  - task: CMake@1
    displayName: CMake DirectXTex (Build) w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out5 -v
