#pragma once

#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_layer.pb.h"

namespace LS
{

class LSLayer : public PB::Module
{
private:
    virtual RequestList GetRequestHandlers() const override;

public:
    struct CreateLayer : public PB::RequestHandler<ls_layer::CreateLayer>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct DestoryLayer : public PB::RequestHandler<ls_layer::DestoryLayer>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetLayerInfo : public PB::RequestHandler<ls_layer::SetLayerInfo>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetLayerInfo : public PB::RequestHandler<ls_layer::GetLayerInfo>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SelectLayer : public PB::RequestHandler<ls_layer::SelectLayer>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct UnSelectLayer : public PB::RequestHandler<ls_layer::UnSelectLayer>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct MoveLayerOrder : public PB::RequestHandler<ls_layer::MoveLayerOrder>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct ClipLayer : public PB::RequestHandler<ls_layer::ClipLayer>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetLayerSnapshot : public PB::RequestHandler<ls_layer::GetLayerSnapshot>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetLayerSnapshot2 : public PB::RequestHandler<ls_layer::GetLayerSnapshot2>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct UpdateLayerSource : public PB::RequestHandler<ls_layer::UpdateLayerSource>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct AddFilter : public PB::RequestHandler<ls_layer::AddFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveFilter : public PB::RequestHandler<ls_layer::RemoveFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct ResetFilterOrder : public PB::RequestHandler<ls_layer::ResetFilterOrder>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    static void SetLayerUniformInfo(LAYER_INFO& layerInfo, const ls_layer::LayerInfo& layer_info, UINT64* cmd = NULL);
    static void GetLayerUniformInfo(const CANVAS_INFO& canvasInfo, const LAYER_INFO& layerInfo, ls_layer::LayerInfo& layer_info);
};
} // namespace MediaSDK