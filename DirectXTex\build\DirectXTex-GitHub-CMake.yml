# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# https://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library using CMake with VS Generator (GitHub Actions covers Ninja).

schedules:
- cron: "0 5 * * *"
  displayName: 'Nightly build'
  branches:
    include:
    - main

trigger:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - '.github/*'
    - '.nuget/*'
    - build/*.cmd
    - build/OneFuzz*.json
    - build/*.props
    - build/*.ps1
    - build/*.targets

pr:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - '.github/*'
    - '.nuget/*'
    - build/*.cmd
    - build/OneFuzz*.json
    - build/*.props
    - build/*.ps1
    - build/*.targets
  drafts: false

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main
  - repository: vcpkgRepo
    name: Microsoft/vcpkg
    type: github
    endpoint: microsoft
    ref: refs/tags/$(VCPKG_TAG)

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

variables:
  Codeql.Enabled: false
  VCPKG_ROOT: $(Build.SourcesDirectory)/vcpkg
  VCPKG_CMAKE_DIR: $(Build.SourcesDirectory)/vcpkg/scripts/buildsystems/vcpkg.cmake
  VCPKG_MANIFEST_DIR: $(Build.SourcesDirectory)/build
  VS_GENERATOR: 'Visual Studio 16 2019'
  WIN10_SDK: '10.0.19041.0'
  WIN11_SDK: '10.0.22000.0'

pool:
  vmImage: windows-2019

jobs:
- job: CMAKE_BUILD
  displayName: CMake using VS Generator
  steps:
  - checkout: self
    clean: true
    fetchTags: false
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out
        -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=OFF
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Release'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out -v --config RelWithDebInfo
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x86'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A Win32 -B out2
        -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=OFF
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x86 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x86 Release'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2 -v --config RelWithDebInfo
  - task: CMake@1
    displayName: 'CMake (UWP): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out3
        -DCMAKE_SYSTEM_NAME=WindowsStore -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=10.0
  - task: CMake@1
    displayName: 'CMake (UWP): Build x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out3 -v
  - task: CMake@1
    displayName: 'CMake (ClangCl): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -T clangcl -B out4
        -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
  - task: CMake@1
    displayName: 'CMake (ClangCl): Build x64 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (ClangCl): Build x64 Release'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4 -v --config RelWithDebInfo
  - task: CMake@1
    displayName: 'CMake (Win10): Config'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out5
        -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=ON
  - task: CMake@1
    displayName: 'CMake (Win10): Build'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out5 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC Spectre): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out6
        -DENABLE_SPECTRE_MITIGATION=ON -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON
        -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=OFF
  - task: CMake@1
    displayName: 'CMake (MSVC Spectre): Build x64 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out6 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC Spectre): Build x64 Release'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out6 -v --config RelWithDebInfo
  - task: CMake@1
    displayName: 'CMake (Win10 Spectre): Config'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out7
        -DENABLE_SPECTRE_MITIGATION=ON -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON
        -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=ON
  - task: CMake@1
    displayName: 'CMake (Win10 Spectre): Build'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out7 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (NO_WCHAR_T): Config'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out8
        -DNO_WCHAR_T=ON -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON
        -DCMAKE_SYSTEM_VERSION=$(WIN11_SDK)
        -DBUILD_DX12=ON
  - task: CMake@1
    displayName: 'CMake (NO_WCHAR_T): Build'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out8 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (DLL): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out9
        -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DBUILD_DX12=ON
        -DBUILD_SHARED_LIBS=ON
  - task: CMake@1
    displayName: 'CMake (DLL): Build x64 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out9 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (DLL): Build x64 Release'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out9 -v --config RelWithDebInfo
  - task: CMake@1
    displayName: 'CMake (UWP DLL): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out10
        -DCMAKE_SYSTEM_NAME=WindowsStore -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON -DCMAKE_SYSTEM_VERSION=10.0
        -DBUILD_SHARED_LIBS=ON
  - task: CMake@1
    displayName: 'CMake (UWP DLL): Build x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out10 -v

- job: CMAKE_BUILD_VCPKG
  displayName: CMake using VCPKG
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: vcpkgRepo
    displayName: Fetch VCPKG
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/vcpkg'
  - task: CmdLine@2
    displayName: VCPKG Bootstrap
    inputs:
      script: call bootstrap-vcpkg.bat
      workingDirectory: $(VCPKG_ROOT)
  - task: CmdLine@2
    displayName: VCPKG install packages
    inputs:
      script: call vcpkg install --x-manifest-root=$(VCPKG_MANIFEST_DIR) --triplet=x64-windows
      workingDirectory: $(VCPKG_ROOT)
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x64'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-windows
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Debug'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x64 w/ OpenEXR'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out2 -DENABLE_OPENEXR_SUPPORT=ON -DBUILD_TESTING=OFF -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-windows
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Debug w/ OpenEXR'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x64 w/ libjpeg'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out3 -DENABLE_LIBJPEG_SUPPORT=ON -DBUILD_TESTING=OFF -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-windows
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Debug w/ libjpeg'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out3 -v --config Debug
  - task: CMake@1
    displayName: 'CMake (MSVC): Config x64 w/ libpng'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -G "$(VS_GENERATOR)" -A x64 -B out4 -DENABLE_LIBPNG_SUPPORT=ON -DBUILD_TESTING=OFF -DCMAKE_SYSTEM_VERSION=$(WIN10_SDK)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-windows
  - task: CMake@1
    displayName: 'CMake (MSVC): Build x64 Debug w/ libpng'
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4 -v --config Debug
