﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f4d68f4f-adbe-40a1-b052-f2e4cae3b5ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{b42472b0-7a63-47b0-b77f-4ffe492471a0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Shaders">
      <UniqueIdentifier>{1838e3e6-1f80-4713-9a98-41ea7e654d12}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f4d68f4f-adbe-40a1-b052-f2e4cae3b5ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{b42472b0-7a63-47b0-b77f-4ffe492471a0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Shaders">
      <UniqueIdentifier>{1838e3e6-1f80-4713-9a98-41ea7e654d12}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Shaders\Compiled">
      <UniqueIdentifier>{7c13ba68-1ec8-4710-a8dd-cd973621b725}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Shaders\Symbols">
      <UniqueIdentifier>{fbc9373c-d511-4fd1-a7b8-d55df1b27d2e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BC4BC5.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BC6HBC7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BCDirectCompute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexCompress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexCompressGPU.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexConvert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexD3D11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexDDS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexFlipRotate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMipmaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMisc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexNormalMaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexPMAlpha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexResize.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexTGA.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexWIC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexHDR.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexD3D12.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="DirectXTex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BC.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="BCDirectCompute.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="DDS.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="DirectXTexP.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="filters.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="scoped.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\d3dx12.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="DirectXTex.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="Shaders\CompileShaders.cmd">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC6HEncode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC7Encode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
  </ItemGroup>
</Project>