﻿{
  "version": 2,
  "configurePresets": [
    {
      "name": "base",
      "displayName": "Basic Config",
      "description": "Basic build using Ninja generator",
      "generator": "Ninja",
      "hidden": true,
      "binaryDir": "${sourceDir}/out/build/${presetName}",
      "cacheVariables": { "CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}" }
    },

    {
      "name": "x64",
      "architecture": {
        "value": "x64",
        "strategy": "external"
      },
      "cacheVariables": { "DIRECTX_ARCH": "x64" },
      "hidden": true
    },
    {
      "name": "x86",
      "architecture": {
        "value": "x86",
        "strategy": "external"
      },
      "cacheVariables": { "DIRECTX_ARCH": "x86" },
      "hidden": true
    },
    {
      "name": "ARM64",
      "architecture": {
        "value": "arm64",
        "strategy": "external"
      },
      "cacheVariables": { "DIRECTX_ARCH": "arm64" },
      "hidden": true
    },
    {
      "name": "ARM64EC",
      "architecture": {
        "value": "arm64ec",
        "strategy": "external"
      },
      "cacheVariables": { "DIRECTX_ARCH": "arm64ec" },
      "environment": {
        "CFLAGS": "/arm64EC",
        "CXXFLAGS": "/arm64EC"
      },
      "hidden": true
    },

    {
      "name": "Debug",
      "cacheVariables": { "CMAKE_BUILD_TYPE": "Debug" },
      "hidden": true
    },
    {
      "name": "Release",
      "cacheVariables":
      {
          "CMAKE_BUILD_TYPE": "RelWithDebInfo",
          "CMAKE_INTERPROCEDURAL_OPTIMIZATION": true
      },
      "hidden": true
    },

    {
      "name": "MSVC",
      "hidden": true,
      "cacheVariables": {
        "CMAKE_CXX_COMPILER": "cl.exe"
      },
      "toolset": {
        "value": "host=x64",
        "strategy": "external"
      }
    },
    {
      "name": "Clang",
      "hidden": true,
      "cacheVariables": {
        "CMAKE_CXX_COMPILER": "clang-cl.exe"
      },
      "toolset": {
        "value": "host=x64",
        "strategy": "external"
      }
    },
    {
      "name": "Clang-X86",
      "environment": {
        "CFLAGS": "-m32",
        "CXXFLAGS": "-m32"
      },
      "hidden": true
    },
    {
      "name": "Clang-AArch64",
      "environment": {
        "CFLAGS": "--target=arm64-pc-windows-msvc",
        "CXXFLAGS": "--target=arm64-pc-windows-msvc"
      },
      "hidden": true
    },
    {
      "name": "GNUC",
      "hidden": true,
      "cacheVariables": {
        "CMAKE_CXX_COMPILER": "g++.exe"
      },
      "toolset": {
        "value": "host=x64",
        "strategy": "external"
      }
    },

    {
      "name": "Win8",
      "cacheVariables": {
        "BUILD_DX12": false
      },
      "hidden": true
    },
    {
      "name": "GDKX",
      "cacheVariables": {
        "BUILD_XBOX_EXTS_XBOXONE": true
      },
      "environment": {
        "XboxOneXDKLatest": ""
      },
      "hidden": true
    },
    {
      "name": "GDKXS",
      "cacheVariables": {
        "BUILD_XBOX_EXTS_SCARLETT": true
      },
      "hidden": true
    },
    {
      "name": "XDK",
      "cacheVariables": {
        "BUILD_XBOX_EXTS_XBOXONE": true
      },
      "environment": {
        "GameDKLatest": ""
      },
      "hidden": true
    },
    {
      "name": "UWP",
      "cacheVariables": {
        "CMAKE_SYSTEM_NAME": "WindowsStore",
        "CMAKE_SYSTEM_VERSION": "10.0",
        "BUILD_DX12": true
      },
      "hidden": true
    },
    {
      "name": "Scarlett",
      "cacheVariables": {
        "XBOX_CONSOLE_TARGET": "scarlett"
      },
      "hidden": true
    },
    {
      "name": "XboxOne",
      "cacheVariables": {
        "XBOX_CONSOLE_TARGET": "xboxone"
      },
      "hidden": true
    },
    {
      "name": "Durango",
      "cacheVariables": {
        "XBOX_CONSOLE_TARGET": "durango"
      },
      "hidden": true
    },
    {
      "name": "VCPKG",
      "cacheVariables": {
        "CMAKE_TOOLCHAIN_FILE": {
          "value": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake",
          "type": "FILEPATH"
        }
      },
      "hidden": true
    },
    {
      "name": "EXR",
      "cacheVariables": {
        "ENABLE_OPENEXR_SUPPORT": true
      },
      "hidden": true
    },
    {
      "name": "JPEG_PNG",
      "cacheVariables": {
        "ENABLE_LIBJPEG_SUPPORT": true,
        "ENABLE_LIBPNG_SUPPORT": true
      },
      "hidden": true
    },
    {
      "name": "MinGW32",
      "hidden": true,
      "environment": {
        "PATH": "$penv{PATH};c:/mingw32/bin;c:/mingw32/libexec/gcc/i686-w64-mingw32/12.2.0"
      },
      "cacheVariables": {
        "VCPKG_TARGET_TRIPLET": "x86-mingw-static",
        "VCPKG_HOST_TRIPLET": "x64-windows"
      }
    },
    {
      "name": "MinGW64",
      "hidden": true,
      "environment": {
        "PATH": "$penv{PATH};c:/mingw64/bin;c:/mingw64/libexec/gcc/x86_64-w64-mingw32/12.2.0"
      },
      "cacheVariables": {
        "VCPKG_TARGET_TRIPLET": "x64-mingw-static",
        "VCPKG_HOST_TRIPLET": "x64-windows"
      }
    },
    {
      "name": "Intel",
      "hidden": true,
      "cacheVariables": {
        "CMAKE_CXX_COMPILER": "icl.exe",
        "BUILD_TOOLS": false
      },
      "toolset": {
        "value": "host=x64",
        "strategy": "external"
      }
    },
    {
      "name": "IntelLLVM",
      "hidden": true,
      "cacheVariables": {
        "CMAKE_CXX_COMPILER": "icx.exe"
      },
      "toolset": {
        "value": "host=x64",
        "strategy": "external"
      }
    },

    {
      "name": "Analyze",
      "cacheVariables":
      {
        "ENABLE_CODE_ANALYSIS": true
      },
      "hidden": true
    },
    {
      "name": "Coverage",
      "cacheVariables":
      {
        "BUILD_TOOLS": false,
        "ENABLE_CODE_COVERAGE": true
      },
      "hidden": true
    },
    {
      "name": "Fuzzing",
      "cacheVariables": {
        "BUILD_FUZZING": true,
        "BUILD_TESTING": false
      },
      "hidden": true
    },

    { "name": "x64-Debug"      , "description": "MSVC for x64 (Debug) with DX12", "inherits": [ "base", "x64", "Debug", "MSVC" ] },
    { "name": "x64-Release"    , "description": "MSVC for x64 (Release) with DX12", "inherits": [ "base", "x64", "Release", "MSVC" ] },
    { "name": "x86-Debug"      , "description": "MSVC for x86 (Debug) with DX12", "inherits": [ "base", "x86", "Debug", "MSVC" ] },
    { "name": "x86-Release"    , "description": "MSVC for x86 (Release) with DX12", "inherits": [ "base", "x86", "Release", "MSVC" ] },
    { "name": "arm64-Debug"    , "description": "MSVC for ARM64 (Debug) with DX12", "inherits": [ "base", "ARM64", "Debug", "MSVC" ] },
    { "name": "arm64-Release"  , "description": "MSVC for ARM64 (Release) with DX12", "inherits": [ "base", "ARM64", "Release", "MSVC" ] },
    { "name": "arm64ec-Debug"  , "description": "MSVC for ARM64EC (Debug) with DX12", "inherits": [ "base", "ARM64EC", "Debug", "MSVC" ] },
    { "name": "arm64ec-Release", "description": "MSVC for ARM64EC (Release) with DX12", "inherits": [ "base", "ARM64EC", "Release", "MSVC" ] },

    { "name": "x64-Debug-UWP"    , "description": "MSVC for x64 (Debug) for UWP", "inherits": [ "base", "x64", "Debug", "MSVC", "UWP" ] },
    { "name": "x64-Release-UWP"  , "description": "MSVC for x64 (Release) for UWP", "inherits": [ "base", "x64", "Release", "MSVC", "UWP" ] },
    { "name": "x86-Debug-UWP"    , "description": "MSVC for x86 (Debug) for UWP", "inherits": [ "base", "x86", "Debug", "MSVC", "UWP" ] },
    { "name": "x86-Release-UWP"  , "description": "MSVC for x86 (Release) for UWP", "inherits": [ "base", "x86", "Release", "MSVC", "UWP" ] },
    { "name": "arm64-Debug-UWP"  , "description": "MSVC for ARM64 (Debug) for UWP", "inherits": [ "base", "ARM64", "Debug", "MSVC", "UWP" ] },
    { "name": "arm64-Release-UWP", "description": "MSVC for ARM64 (Release) for UWP", "inherits": [ "base", "ARM64", "Release", "MSVC", "UWP" ] },

    { "name": "x64-Debug-Win8"    , "description": "MSVC for x64 (Debug) for Windows 8.1", "inherits": [ "base", "x64", "Debug", "MSVC", "Win8" ] },
    { "name": "x64-Release-Win8"  , "description": "MSVC for x64 (Release) for Windows 8.1", "inherits": [ "base", "x64", "Release", "MSVC", "Win8" ] },
    { "name": "x86-Debug-Win8"    , "description": "MSVC for x86 (Debug) for Windows 8.1", "inherits": [ "base", "x86", "Debug", "MSVC", "Win8" ] },
    { "name": "x86-Release-Win8"  , "description": "MSVC for x86 (Release) for Windows 8.1", "inherits": [ "base", "x86", "Release", "MSVC", "Win8" ] },

    { "name": "x64-Debug-Scarlett"    , "description": "MSVC for x64 (Debug) for Xbox Series X|S", "inherits": [ "base", "x64", "Debug", "MSVC", "Scarlett" ] },
    { "name": "x64-Release-Scarlett"  , "description": "MSVC for x64 (Release) for Xbox Series X|S", "inherits": [ "base", "x64", "Release", "MSVC", "Scarlett" ] },

    { "name": "x64-Debug-XboxOne"    , "description": "MSVC for x64 (Debug) for Xbox One", "inherits": [ "base", "x64", "Debug", "MSVC", "XboxOne" ] },
    { "name": "x64-Release-XboxOne"  , "description": "MSVC for x64 (Release) for Xbox One", "inherits": [ "base", "x64", "Release", "MSVC", "XboxOne" ] },

    { "name": "x64-Debug-Durango"    , "description": "MSVC for x64 (Debug) for legacy Xbox One XDK", "inherits": [ "base", "x64", "Debug", "MSVC", "Durango" ] },
    { "name": "x64-Release-Durango"  , "description": "MSVC for x64 (Release) for legacy Xbox One XDK", "inherits": [ "base", "x64", "Release", "MSVC", "Durango" ] },

    { "name": "x64-Debug-VCPKG"      , "description": "MSVC for x64 (Debug) using VCPKG", "inherits": [ "base", "x64", "Debug", "MSVC", "VCPKG" ] },
    { "name": "x64-Release-VCPKG"    , "description": "MSVC for x64 (Release) using VCPKG", "inherits": [ "base", "x64", "Release", "MSVC", "VCPKG" ] },
    { "name": "x86-Debug-VCPKG"      , "description": "MSVC for x86 (Debug) using VCPKG", "inherits": [ "base", "x86", "Debug", "MSVC", "VCPKG" ] },
    { "name": "x86-Release-VCPKG"    , "description": "MSVC for x86 (Release) using VCPKG", "inherits": [ "base", "x86", "Release", "MSVC", "VCPKG" ] },
    { "name": "arm64-Debug-VCPKG"    , "description": "MSVC for ARM64 (Debug) using VCPKG", "inherits": [ "base", "ARM64", "Debug", "MSVC", "VCPKG" ] },
    { "name": "arm64-Release-VCPKG"  , "description": "MSVC for ARM64 (Release) using VCPKG", "inherits": [ "base", "ARM64", "Release", "MSVC", "VCPKG" ] },
    { "name": "arm64ec-Debug-VCPKG"  , "description": "MSVC for ARM64EC (Debug) using VCPKG", "inherits": [ "base", "ARM64EC", "Debug", "MSVC", "VCPKG" ], "cacheVariables": { "VCPKG_TARGET_TRIPLET": "arm64ec-windows" } },
    { "name": "arm64ec-Release-VCPKG", "description": "MSVC for ARM64EC (Release) using VCPKG", "inherits": [ "base", "ARM64EC", "Release", "MSVC", "VCPKG" ], "cacheVariables": { "VCPKG_TARGET_TRIPLET": "arm64ec-windows" } },

    { "name": "x64-Debug-EXR"      , "description": "MSVC for x64 (Debug) using VCPKG/OpenEXR", "inherits": [ "base", "x64", "Debug", "MSVC", "VCPKG", "EXR" ] },
    { "name": "x64-Release-EXR"    , "description": "MSVC for x64 (Release) using VCPKG/OpenEXR", "inherits": [ "base", "x64", "Release", "MSVC", "VCPKG", "EXR" ] },

    { "name": "x64-Debug-JPEG-PNG"  , "description": "MSVC for x64 (Debug) using VCPKG/PNG/JPEG", "inherits": [ "base", "x64", "Debug", "MSVC", "VCPKG", "JPEG_PNG" ] },
    { "name": "x64-Release-JPEG-PNG", "description": "MSVC for x64 (Release) using VCPKG/PNG/JPEG", "inherits": [ "base", "x64", "Release", "MSVC", "VCPKG", "JPEG_PNG" ] },

    { "name": "x64-Debug-Clang"    , "description": "Clang/LLVM for x64 (Debug) with DX12", "inherits": [ "base", "x64", "Debug", "Clang" ] },
    { "name": "x64-Release-Clang"  , "description": "Clang/LLVM for x64 (Release) with DX12", "inherits": [ "base", "x64", "Release", "Clang" ] },
    { "name": "x86-Debug-Clang"    , "description": "Clang/LLVM for x86 (Debug) with DX12", "inherits": [ "base", "x86", "Debug", "Clang", "Clang-X86" ] },
    { "name": "x86-Release-Clang"  , "description": "Clang/LLVM for x86 (Release) with DX12", "inherits": [ "base", "x86", "Release", "Clang", "Clang-X86" ] },
    { "name": "arm64-Debug-Clang"  , "description": "Clang/LLVM for AArch64 (Debug) with DX12", "inherits": [ "base", "ARM64", "Debug", "Clang", "Clang-AArch64" ] },
    { "name": "arm64-Release-Clang", "description": "Clang/LLVM for AArch64 (Release) with DX12", "inherits": [ "base", "ARM64", "Release", "Clang", "Clang-AArch64" ] },

    { "name": "x64-Debug-UWP-Clang"    , "description": "Clang/LLVM for x64 (Debug) for UWP", "inherits": [ "base", "x64", "Debug", "Clang", "UWP" ] },
    { "name": "x64-Release-UWP-Clang"  , "description": "Clang/LLVM for x64 (Release) for UWP", "inherits": [ "base", "x64", "Release", "Clang", "UWP" ] },
    { "name": "x86-Debug-UWP-Clang"    , "description": "Clang/LLVM for x86 (Debug) for UWP", "inherits": [ "base", "x86", "Debug", "Clang", "Clang-X86", "UWP" ] },
    { "name": "x86-Release-UWP-Clang"  , "description": "Clang/LLVM for x86 (Release) for UWP", "inherits": [ "base", "x86", "Release", "Clang", "Clang-X86", "UWP" ] },
    { "name": "arm64-Debug-UWP-Clang"  , "description": "Clang/LLVM for AArch64 (Debug) for UWP", "inherits": [ "base", "ARM64", "Debug", "Clang", "Clang-AArch64", "UWP" ] },
    { "name": "arm64-Release-UWP-Clang", "description": "Clang/LLVM for AArch64 (Release) for UWP", "inherits": [ "base", "ARM64", "Release", "Clang", "Clang-AArch64", "UWP" ] },

    { "name": "x64-Debug-Win8-Clang"  , "description": "Clang/LLVM for x64 (Debug) for Windows 8.1", "inherits": [ "base", "x64", "Debug", "Clang", "Win8" ] },
    { "name": "x64-Release-Win8-Clang", "description": "Clang/LLVM for x64 (Release) for Windows 8.1", "inherits": [ "base", "x64", "Release", "Clang", "Win8" ] },
    { "name": "x86-Debug-Win8-Clang"  , "description": "Clang/LLVM for x86 (Debug) for Windows 8.1", "inherits": [ "base", "x86", "Debug", "Clang", "Clang-X86", "Win8" ] },
    { "name": "x86-Release-Win8-Clang", "description": "Clang/LLVM for x86 (Release) for Windows 8.1", "inherits": [ "base", "x86", "Release", "Clang", "Clang-X86", "Win8" ] },

    { "name": "x64-Debug-Clang-VCPKG"    , "description": "Clang/LLVM for x64 (Debug) using VCPKG/OpenEXR", "inherits": [ "base", "x64", "Debug", "Clang", "VCPKG" ] },
    { "name": "x64-Release-Clang-VCPKG"  , "description": "Clang/LLVM for x64 (Release) using VCPKG/OpenEXR", "inherits": [ "base", "x64", "Release", "Clang", "VCPKG" ] },
    { "name": "x86-Debug-Clang-VCPKG"    , "description": "Clang/LLVM for x86 (Debug) using VCPKG/OpenEXR", "inherits": [ "base", "x86", "Debug", "Clang", "Clang-X86", "VCPKG" ] },
    { "name": "x86-Release-Clang-VCPKG"  , "description": "Clang/LLVM for x86 (Release) using VCPKG/OpenEXR", "inherits": [ "base", "x86", "Release", "Clang", "Clang-X86", "VCPKG" ] },
    { "name": "arm64-Debug-Clang-VCPKG"  , "description": "Clang/LLVM for AArch64 (Debug) using VCPKG/OpenEXR", "inherits": [ "base", "ARM64", "Debug", "Clang", "Clang-AArch64", "VCPKG" ] },
    { "name": "arm64-Release-Clang-VCPKG", "description": "Clang/LLVM for AArch64 (Release) using VCPKG/OpenEXR", "inherits": [ "base", "ARM64", "Release", "Clang", "Clang-AArch64", "VCPKG" ] },

    { "name": "x64-Debug-Clang-JPEG-PNG"   , "description": "Clang/LLVM for x64 (Debug) with DX12", "inherits": [ "base", "x64", "Debug", "Clang", "VCPKG", "JPEG_PNG" ] },
    { "name": "x64-Release-Clang-JPEG-PNG" , "description": "Clang/LLVM for x64 (Release) with DX12", "inherits": [ "base", "x64", "Release", "Clang", "VCPKG", "JPEG_PNG" ] },

    { "name": "x64-Debug-GDKX"     , "description": "MSVC for x64 (Debug) Xbox One Extensions", "inherits": [ "base", "x64", "Debug", "GDKX", "MSVC" ] },
    { "name": "x64-Release-GDKX"   , "description": "MSVC for x64 (Release) Xbox One Extensions", "inherits": [ "base", "x64", "Release", "GDKX", "MSVC" ] },
    { "name": "x64-Debug-GDKX-S"   , "description": "MSVC for x64 (Debug) Xbox Series X|S Extensions", "inherits": [ "base", "x64", "Debug", "GDKXS", "MSVC" ] },
    { "name": "x64-Release-GDKX-S" , "description": "MSVC for x64 (Release) Xbox Series X|S Extensions", "inherits": [ "base", "x64", "Release", "GDKXS", "MSVC" ] },
    { "name": "x64-Debug-XDK"      , "description": "MSVC for x64 (Debug) Xbox One Extensions (legacy)", "inherits": [ "base", "x64", "Debug", "XDK", "MSVC" ] },
    { "name": "x64-Release-XDK"    , "description": "MSVC for x64 (Release) Xbox One Extensions (legacy)", "inherits": [ "base", "x64", "Release", "XDK", "MSVC" ] },

    { "name": "x64-Debug-GDKX-Clang"     , "description": "MSVC for x64 (Debug) Xbox One Extensions", "inherits": [ "base", "x64", "Debug", "GDKX", "Clang" ] },
    { "name": "x64-Release-GDKX-Clang"   , "description": "MSVC for x64 (Release) Xbox One Extensions", "inherits": [ "base", "x64", "Release", "GDKX", "Clang" ] },
    { "name": "x64-Debug-GDKX-S-Clang"   , "description": "MSVC for x64 (Debug) Xbox Series X|S Extensions", "inherits": [ "base", "x64", "Debug", "GDKXS", "Clang" ] },
    { "name": "x64-Release-GDKX-S-Clang" , "description": "MSVC for x64 (Release) Xbox Series X|S Extensions", "inherits": [ "base", "x64", "Release", "GDKXS", "Clang" ] },

    { "name": "x64-Debug-Scarlett-Clang"  , "description": "Clang/LLVM for x64 (Debug) for Xbox Series X|S", "inherits": [ "base", "x64", "Debug", "Clang", "Scarlett" ] },
    { "name": "x64-Release-Scarlett-Clang", "description": "Clang/LLVM for x64 (Release) for Xbox Series X|S", "inherits": [ "base", "x64", "Release", "Clang", "Scarlett" ] },

    { "name": "x64-Debug-XboxOne-Clang"   , "description": "Clang/LLVM for x64 (Debug) for Xbox One", "inherits": [ "base", "x64", "Debug", "Clang", "XboxOne" ] },
    { "name": "x64-Release-XboxOne-Clang" , "description": "Clang/LLVM for x64 (Release) for Xbox One", "inherits": [ "base", "x64", "Release", "Clang", "XboxOne" ] },

    { "name": "x64-Debug-MinGW"  , "description": "MinG-W64 (Debug)", "inherits": [ "base", "x64", "Debug", "GNUC", "VCPKG", "MinGW64" ] },
    { "name": "x64-Release-MinGW", "description": "MinG-W64 (Release)", "inherits": [ "base", "x64", "Release", "GNUC", "VCPKG", "MinGW64" ] },
    { "name": "x86-Debug-MinGW"  , "description": "MinG-W32 (Debug)", "inherits": [ "base", "x86", "Debug", "GNUC", "VCPKG", "MinGW32" ] },
    { "name": "x86-Release-MinGW", "description": "MinG-W32 (Release)", "inherits": [ "base", "x86", "Release", "GNUC", "VCPKG", "MinGW32" ] },

    { "name": "x64-Debug-MinGW-JPEG-PNG"  , "description": "MinG-W64 (Debug)", "inherits": [ "base", "x64", "Debug", "GNUC", "VCPKG", "MinGW64", "JPEG_PNG" ] },
    { "name": "x64-Release-MinGW-JPEG-PNG", "description": "MinG-W64 (Release)", "inherits": [ "base", "x64", "Release", "GNUC", "VCPKG", "MinGW64", "JPEG_PNG" ] },
    { "name": "x86-Debug-MinGW-JPEG-PNG"  , "description": "MinG-W32 (Debug)", "inherits": [ "base", "x86", "Debug", "GNUC", "VCPKG", "MinGW32", "JPEG_PNG" ] },
    { "name": "x86-Release-MinGW-JPEG-PNG", "description": "MinG-W32 (Release)", "inherits": [ "base", "x86", "Release", "GNUC", "VCPKG", "MinGW32", "JPEG_PNG" ] },

    { "name": "x64-Debug-Linux",     "description": "WSL Linux x64 (Debug)", "inherits": [ "base", "x64", "Debug", "VCPKG", "JPEG_PNG" ] },
    { "name": "x64-Release-Linux",   "description": "WSL Linux x64 (Release)", "inherits": [ "base", "x64", "Release", "VCPKG", "JPEG_PNG" ] },
    { "name": "arm64-Debug-Linux",   "description": "WSL Linux ARM64 (Debug)", "inherits": [ "base", "ARM64", "Debug", "VCPKG", "JPEG_PNG" ] },
    { "name": "arm64-Release-Linux", "description": "WSL Linux ARM64 (Release)", "inherits": [ "base", "ARM64", "Release", "VCPKG", "JPEG_PNG" ] },

    { "name": "x64-Debug-ICC"     , "description": "Intel Classic Compiler (Debug) with DX12", "inherits": [ "base", "x64", "Debug", "Intel" ] },
    { "name": "x64-Release-ICC"   , "description": "Intel Classic Compiler (Release) with DX12", "inherits": [ "base", "x64", "Release", "Intel" ] },

    { "name": "x64-Debug-ICX"    , "description": "Intel oneAPI Compiler (Debug) with DX12", "inherits": [ "base", "x64", "Debug", "IntelLLVM" ] },
    { "name": "x64-Release-ICX"  , "description": "Intel oneAPI Compiler (Release) with DX12", "inherits": [ "base", "x64", "Release", "IntelLLVM" ] },

    { "name": "x64-Analyze"       , "description": "MSVC for x64 (Debug) with DX12 using /analyze", "inherits": [ "base", "x64", "Debug", "MSVC", "Analyze" ] },
    { "name": "x64-Coverage"      , "description": "MSVC for x64 (Debug) with DX12 w/ Code Coverage", "inherits": [ "base", "x64", "Debug", "MSVC", "Coverage" ] },
    { "name": "x64-Coverage-Clang", "description": "Clang/LLVM for x64 (Debug) with DX12 w/ Code Coverage", "inherits": [ "base", "x64", "Debug", "Clang", "Coverage" ] },
    { "name": "x64-Fuzzing"       , "description": "MSVC for x64 (Release) with ASan", "inherits": [ "base", "x64", "Release", "MSVC", "Fuzzing" ] }
  ],
  "testPresets": [
    { "name": "x64-Debug"      , "configurePreset": "x64-Debug" },
    { "name": "x64-Release"    , "configurePreset": "x64-Release" },
    { "name": "x86-Debug"      , "configurePreset": "x86-Debug" },
    { "name": "x86-Release"    , "configurePreset": "x86-Release" },
    { "name": "arm64-Debug"    , "configurePreset": "arm64-Debug" },
    { "name": "arm64-Release"  , "configurePreset": "arm64-Release" },
    { "name": "arm64ec-Debug"  , "configurePreset": "arm64ec-Debug" },
    { "name": "arm64ec-Release", "configurePreset": "arm64ec-Release" },

    { "name": "x64-Debug-VCPKG"      , "configurePreset": "x64-Debug-VCPKG" },
    { "name": "x64-Release-VCPKG"    , "configurePreset": "x64-Release-VCPKG" },
    { "name": "x86-Debug-VCPKG"      , "configurePreset": "x86-Debug-VCPKG" },
    { "name": "x86-Release-VCPKG"    , "configurePreset": "x86-Release-VCPKG" },
    { "name": "arm64-Debug-VCPKG"    , "configurePreset": "arm64-Debug-VCPKG" },
    { "name": "arm64-Release-VCPKG"  , "configurePreset": "arm64-Release-VCPKG" },
    { "name": "arm64ec-Debug-VCPKG"  , "configurePreset": "arm64ec-Debug-VCPKG" },
    { "name": "arm64ec-Release-VCPKG", "configurePreset": "arm64ec-Release-VCPKG" },

    { "name": "x64-Debug-Clang"    , "configurePreset": "x64-Debug-Clang" },
    { "name": "x64-Release-Clang"  , "configurePreset": "x64-Release-Clang" },
    { "name": "x86-Debug-Clang"    , "configurePreset": "x86-Debug-Clang" },
    { "name": "x86-Release-Clang"  , "configurePreset": "x86-Release-Clang" },
    { "name": "arm64-Debug-Clang"  , "configurePreset": "arm64-Debug-Clang" },
    { "name": "arm64-Release-Clang", "configurePreset": "arm64-Release-Clang" },

    { "name": "x64-Debug-MinGW"    , "configurePreset": "x64-Debug-MinGW" },
    { "name": "x64-Release-MinGW"  , "configurePreset": "x64-Release-MinGW" },
    { "name": "x86-Debug-MinGW"    , "configurePreset": "x86-Debug-MinGW" },
    { "name": "x86-Release-MinGW"  , "configurePreset": "x86-Release-MinGW" },

    { "name": "x64-Debug-ICC"    , "configurePreset": "x64-Debug-ICC" },
    { "name": "x64-Release-ICC"  , "configurePreset": "x64-Release-ICC"},

    { "name": "x64-Debug-ICX"    , "configurePreset": "x64-Debug-ICX" },
    { "name": "x64-Release-ICX"  , "configurePreset": "x64-Release-ICX"},

    { "name": "x64-Debug-JPEG-PNG"  , "configurePreset": "x64-Debug-JPEG-PNG" },
    { "name": "x64-Release-JPEG-PNG", "configurePreset": "x64-Release-JPEG-PNG"}
  ]
}