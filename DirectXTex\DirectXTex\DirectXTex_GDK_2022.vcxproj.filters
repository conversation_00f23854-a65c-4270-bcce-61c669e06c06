﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files\Shaders">
      <UniqueIdentifier>{c60baf7a-25a9-4215-842d-8d49d65d538e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Auxiliary">
      <UniqueIdentifier>{34568028-594c-4052-9a41-0973bbe526e1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="DirectXTex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BC.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="DDS.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="DirectXTexP.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="filters.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="scoped.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Auxiliary\DirectXTexXbox.h">
      <Filter>Auxiliary</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\d3dx12.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="DirectXTex.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="Shaders\CompileShaders.cmd">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC7Encode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC6HEncode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="..\README.md" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BC4BC5.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BC6HBC7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexCompress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexConvert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexDDS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexFlipRotate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMipmaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMisc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexNormalMaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexPMAlpha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexResize.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexTGA.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexWIC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexHDR.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexD3D12.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxTile.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxD3D12X.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxDDS.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxDetile.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxImage.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
  </ItemGroup>
</Project>