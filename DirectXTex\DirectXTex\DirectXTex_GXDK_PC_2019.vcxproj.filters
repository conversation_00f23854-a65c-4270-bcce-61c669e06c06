﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns:atg="http://atg.xbox.com" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{68652706-b700-4472-9af7-a56a482bd896}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{9b7fcbc5-2533-4b88-b75b-d4803e55fa7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Shaders">
      <UniqueIdentifier>{eb989628-e889-44bf-837a-05c9f09b258e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Auxiliary">
      <UniqueIdentifier>{1cab8337-50f3-4e55-9ccf-c060bac667ee}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <CLInclude Include="DirectXTex.h">
      <Filter>Header Files</Filter>
    </CLInclude>
    <CLInclude Include="DirectXTex.inl">
      <Filter>Header Files</Filter>
    </CLInclude>
    <ClCompile Include="BC4BC5.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClInclude Include="BCDirectCompute.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <CLInclude Include="DDS.h">
      <Filter>Source Files</Filter>
    </CLInclude>
    <CLInclude Include="DirectXTexP.h">
      <Filter>Source Files</Filter>
    </CLInclude>
    <ClInclude Include="filters.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <CLInclude Include="scoped.h">
      <Filter>Source Files</Filter>
    </CLInclude>
    <ClInclude Include="..\Auxiliary\DirectXTexXbox.h">
      <Filter>Auxiliary</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\d3dx12.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BC6HBC7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BCDirectCompute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexCompress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexCompressGPU.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexConvert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexDDS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexFlipRotate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMipMaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexMisc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexNormalMaps.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexPMAlpha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexResize.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexTGA.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexWIC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirectXTexHDR.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxDDS.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxDetile.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxImage.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
    <ClCompile Include="..\Auxiliary\DirectXTexXboxTile.cpp">
      <Filter>Auxiliary</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CLInclude Include="BC.h">
      <Filter>Source Files</Filter>
    </CLInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Shaders\CompileShaders.cmd">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC6HEncode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="Shaders\BC7Encode.hlsl">
      <Filter>Source Files\Shaders</Filter>
    </None>
    <None Include="..\README.md" />
  </ItemGroup>
</Project>