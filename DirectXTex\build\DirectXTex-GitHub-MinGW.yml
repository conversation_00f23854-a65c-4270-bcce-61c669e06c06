# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# https://go.microsoft.com/fwlink/?LinkId=248926

# Builds the library and test suite using the MinGW compiler.

schedules:
- cron: "0 5 * * *"
  displayName: 'Nightly build'
  branches:
    include:
    - main

trigger:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - '.github/*'
    - '.nuget/*'
    - build/*.cmd
    - build/OneFuzz*.json
    - build/*.props
    - build/*.ps1
    - build/*.targets

pr:
  branches:
    include:
    - main
  paths:
    exclude:
    - '*.md'
    - LICENSE
    - '.github/*'
    - '.nuget/*'
    - build/*.cmd
    - build/OneFuzz*.json
    - build/*.props
    - build/*.ps1
    - build/*.targets
  drafts: false

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main
  - repository: vcpkgRepo
    name: Microsoft/vcpkg
    type: github
    endpoint: microsoft
    ref: refs/tags/$(VCPKG_TAG)
  - repository: testRepo
    name: walbourn/directxtextest
    type: github
    endpoint: microsoft
    ref: refs/heads/main

name: $(Year:yyyy).$(Month).$(DayOfMonth)$(Rev:.r)

pool:
  vmImage: windows-2022

variables:
  Codeql.Enabled: false
  VCPKG_ROOT: $(Build.SourcesDirectory)/vcpkg
  VCPKG_CMAKE_DIR: $(Build.SourcesDirectory)/vcpkg/scripts/buildsystems/vcpkg.cmake
  VCPKG_MANIFEST_DIR: $(Build.SourcesDirectory)/build
  WIN11_SDK: '10.0.22000.0'
  URL_MINGW32: https://github.com/brechtsanders/winlibs_mingw/releases/download/12.2.0-14.0.6-10.0.0-ucrt-r2/winlibs-i686-posix-dwarf-gcc-12.2.0-llvm-14.0.6-mingw-w64ucrt-10.0.0-r2.zip
  HASH_MINGW32: 'fcd1e11b896190da01c83d5b5fb0d37b7c61585e53446c2dab0009debc3915e757213882c35e35396329338de6f0222ba012e23a5af86932db45186a225d1272'
  CompileShadersOutput: $(Build.BinariesDirectory)\Shaders

jobs:
- job: MINGW32_BUILD
  displayName: 'Minimalist GNU for Windows (MinGW32)'
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: vcpkgRepo
    displayName: Fetch VCPKG
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/vcpkg'
  - task: CmdLine@2
    displayName: VCPKG Bootstrap
    inputs:
      script: |
        call bootstrap-vcpkg.bat
        echo ##vso[task.setvariable variable=VCPKG_DEFAULT_TRIPLET;]x86-mingw-static
        echo ##vso[task.setvariable variable=VCPKG_DEFAULT_HOST_TRIPLET;]x86-mingw-static

      workingDirectory: $(Build.SourcesDirectory)\vcpkg
  - task: PowerShell@2
    displayName: Install MinGW32 and setup for Windows 11 SDK
    inputs:
      targetType: inline
      script: |
        $ProgressPreference = 'SilentlyContinue'
        Write-Host "Downloading winlibs..."
        Invoke-WebRequest -Uri "$(URL_MINGW32)" -OutFile "gw32.zip"
        Write-Host "Downloaded."
        $fileHash = Get-FileHash -Algorithm SHA512 gw32.zip | ForEach { $_.Hash} | Out-String
        $filehash = $fileHash.Trim()
        Write-Host "##[debug]SHA512: " $fileHash
        if ($fileHash -ne '$(HASH_MINGW32)') {
            Write-Error -Message "##[error]Computed hash does not match!" -ErrorAction Stop
        }
        Write-Host "Extracting winlibs..."
        Expand-Archive -LiteralPath 'gw32.zip'
        Write-Host "Extracted."
        Write-Host "Added to path: $env:BUILD_SOURCESDIRECTORY\gw32\mingw32\bin"
        Write-Host "##vso[task.prependpath]$env:BUILD_SOURCESDIRECTORY\gw32\mingw32\bin"
        $sdkroot = Get-ItemProperty -Path 'HKLM:\SOFTWARE\Wow6432Node\Microsoft\Windows Kits\Installed Roots' | Select-Object -ExpandProperty KitsRoot10
        $wsdkbin = "{0}bin\" -f $sdkroot
        $wsdkverbin = "{0}bin\$(WIN11_SDK)\" -f $sdkroot
        $wsdkarchbin = "{0}bin\$(WIN11_SDK)\x86" -f $sdkroot
        if (Test-Path "$wsdkarchbin") {
            Write-Host "##vso[task.setvariable variable=WindowsSdkBinPath;]$wsdkbin"
            Write-Host "##vso[task.setvariable variable=WindowsSdkVerBinPath;]$wsdkverbin"
            Write-Host "##vso[task.prependpath]$wsdkarchbin"
        }
        else {
            Write-Error -Message "##[error]Can't find Windows SDK ($(WIN11_SDK))" -ErrorAction Stop
        }

      workingDirectory: $(Build.SourcesDirectory)
  - task: CmdLine@2
    displayName: GCC version
    inputs:
      script: g++ --version
  - task: BatchScript@1
    displayName: CompileShaders
    inputs:
      filename: DirectXTex/Shaders/CompileShaders.cmd
      workingFolder: $(Build.SourcesDirectory)\DirectXTex\Shaders
  - task: BatchScript@1
    displayName: HLSL
    inputs:
      filename: DDSView/hlsl.cmd
      workingFolder: $(Build.SourcesDirectory)\DDSView
  - task: CmdLine@2
    displayName: VCPKG install packages
    inputs:
      script: call vcpkg install --x-manifest-root=$(VCPKG_MANIFEST_DIR) --triplet=x86-mingw-static
      workingDirectory: $(VCPKG_ROOT)
  - task: CMake@1
    displayName: CMake (MinGW32)
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out -DCMAKE_BUILD_TYPE="Debug" -DDIRECTX_ARCH=x86
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x86-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW32) Build
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out
  - task: CMake@1
    displayName: CMake (MinGW32) w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out2 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_OPENEXR_SUPPORT=ON -DDIRECTX_ARCH=x86
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x86-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW32) Build  w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2
  - task: CMake@1
    displayName: CMake (MinGW32) w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out3 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_LIBJPEG_SUPPORT=ON -DDIRECTX_ARCH=x86
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x86-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW32) Build w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out3
  - task: CMake@1
    displayName: CMake (MinGW32) w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out4 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_LIBPNG_SUPPORT=ON -DDIRECTX_ARCH=x86
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x86-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW32) Build w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4

- job: MINGW64_BUILD
  displayName: 'Minimalist GNU for Windows (MinGW-W64) BUILD_TESTING=ON'
  steps:
  - checkout: self
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's'
  - checkout: vcpkgRepo
    displayName: Fetch VCPKG
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/vcpkg'
  - checkout: testRepo
    displayName: Fetch Tests
    clean: true
    fetchTags: false
    fetchDepth: 1
    path: 's/Tests'
  - task: CmdLine@2
    displayName: VCPKG Bootstrap
    inputs:
      script: |
        call bootstrap-vcpkg.bat
        echo ##vso[task.setvariable variable=VCPKG_DEFAULT_TRIPLET;]x64-mingw-static
        echo ##vso[task.setvariable variable=VCPKG_DEFAULT_HOST_TRIPLET;]x64-mingw-static

      workingDirectory: $(Build.SourcesDirectory)\vcpkg
  - task: PowerShell@2
    displayName: Setup for Shader Compilation
    inputs:
      targetType: inline
      script: |
        $ProgressPreference = 'SilentlyContinue'
        $sdkroot = Get-ItemProperty -Path 'HKLM:\SOFTWARE\Wow6432Node\Microsoft\Windows Kits\Installed Roots' | Select-Object -ExpandProperty KitsRoot10
        $wsdkbin = "{0}bin\" -f $sdkroot
        $wsdkverbin = "{0}bin\$(WIN11_SDK)\" -f $sdkroot
        $wsdkarchbin = "{0}bin\$(WIN11_SDK)\x64" -f $sdkroot
        if (Test-Path "$wsdkarchbin") {
            Write-Host "##vso[task.setvariable variable=WindowsSdkBinPath;]$wsdkbin"
            Write-Host "##vso[task.setvariable variable=WindowsSdkVerBinPath;]$wsdkverbin"
            Write-Host "##vso[task.prependpath]$wsdkarchbin"
        }
        else {
            Write-Error -Message "##[error]Can't find Windows SDK ($(WIN11_SDK))" -ErrorAction Stop
        }

  - task: CmdLine@2
    displayName: GCC version
    inputs:
      script: g++ --version
  - task: BatchScript@1
    displayName: CompileShaders.cmd
    inputs:
      filename: DirectXTex/Shaders/CompileShaders.cmd
      workingFolder: $(Build.SourcesDirectory)\DirectXTex\Shaders
  - task: BatchScript@1
    displayName: HLSL
    inputs:
      filename: DDSView/hlsl.cmd
      workingFolder: $(Build.SourcesDirectory)\DDSView
  - task: CmdLine@2
    displayName: VCPKG install packages
    inputs:
      script: call vcpkg install --x-manifest-root=$(VCPKG_MANIFEST_DIR) --triplet=x64-mingw-static
      workingDirectory: $(VCPKG_ROOT)
  - task: CMake@1
    displayName: CMake (MinGW-W64)
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out -DCMAKE_BUILD_TYPE="Debug" -DDIRECTX_ARCH=x64
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW-W64) Build
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out
  - task: CMake@1
    displayName: CMake (MinGW-W64) w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out2 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_OPENEXR_SUPPORT=ON -DDIRECTX_ARCH=x64 -DBUILD_TESTING=OFF
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW-W64) Build  w/ OpenEXR
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out2
  - task: CMake@1
    displayName: CMake (MinGW-W64) w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out3 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_LIBJPEG_SUPPORT=ON -DDIRECTX_ARCH=x64 -DBUILD_TESTING=OFF
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW-W64) Build w/ libjpeg
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out3
  - task: CMake@1
    displayName: CMake (MinGW-W64) w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: >
        -B out4 -DCMAKE_BUILD_TYPE="Debug" -DENABLE_LIBPNG_SUPPORT=ON -DDIRECTX_ARCH=x64 -DBUILD_TESTING=OFF
        -DCMAKE_CXX_COMPILER="g++.exe" -G "MinGW Makefiles"
        -DUSE_PREBUILT_SHADERS=ON -DCOMPILED_SHADERS=$(CompileShadersOutput)
        -DCMAKE_TOOLCHAIN_FILE="$(VCPKG_CMAKE_DIR)" -DVCPKG_MANIFEST_DIR="$(VCPKG_MANIFEST_DIR)" -DVCPKG_TARGET_TRIPLET=x64-mingw-static
  - task: CMake@1
    displayName: CMake (MinGW-W64) Build w/ libpng
    inputs:
      cwd: $(Build.SourcesDirectory)
      cmakeArgs: --build out4
