# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
#
# http://go.microsoft.com/fwlink/?LinkId=248926

name: Microsoft C++ Code Analysis

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
    paths-ignore:
      - '*.md'
      - LICENSE
      - '.nuget/*'
      - build/*.cmd
      - build/*.json
      - build/*.props
      - build/*.ps1
      - build/*.targets
      - build/*.yml
  schedule:
    - cron: '41 16 * * 1'

permissions:
  contents: read

jobs:
  analyze:
    permissions:
      contents: read
      security-events: write
      actions: read
    name: Analyze
    runs-on: windows-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: ilammy/msvc-dev-cmd@0b201ec74fa43914dc39ae48a89fd1d8cb592756 # v1.13.0
        with:
          arch: amd64

      - name: Configure CMake
        working-directory: ${{ github.workspace }}
        run: cmake -B out -DCMAKE_DISABLE_PRECOMPILE_HEADERS=ON

      - name: 'Build Shaders (BC)'
        shell: cmd
        working-directory: ./DirectXTex/Shaders
        run: CompileShaders.cmd
        env:
          CompileShadersOutput: ${{ github.workspace }}/out/Shaders/Compiled

      - name: 'Build Shaders (DDSVIEW)'
        shell: cmd
        working-directory: ./DDSView
        run: hlsl.cmd
        env:
          CompileShadersOutput: ${{ github.workspace }}/out/Shaders/Compiled

      - name: Initialize MSVC Code Analysis
        uses: microsoft/msvc-code-analysis-action@24c285ab36952c9e9182f4b78dfafbac38a7e5ee # v0.1.1
        id: run-analysis
        with:
          cmakeBuildDirectory: ./out
          buildConfiguration: Debug
          ruleset: NativeRecommendedRules.ruleset

      # Upload SARIF file to GitHub Code Scanning Alerts
      - name: Upload SARIF to GitHub
        uses: github/codeql-action/upload-sarif@b56ba49b26e50535fa1e7f7db0f4f7b4bf65d80d # v3.28.10
        with:
          sarif_file: ${{ steps.run-analysis.outputs.sarif }}
